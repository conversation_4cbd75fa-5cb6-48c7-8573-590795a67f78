// @ts-ignore
/* eslint-disable */
import { request } from '@/request';

/** Get Search Terms GET /api/v1/ads/sp/search-terms */
export async function getSearchTerms(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetSearchTermsParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/ads/sp/search-terms', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
