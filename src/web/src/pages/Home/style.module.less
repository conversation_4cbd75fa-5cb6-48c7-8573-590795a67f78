.homePage {
  background-color: #FFFFFF;
  min-height: 100vh;
  padding-bottom: 16px;
}

// 头部导航样式
.header {
  height: 64px;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #FFFFFF;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logoText {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.navLink {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;

  &:hover {
    color: #ED1000;
  }
}

.loginLink {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;

  &:hover {
    color: #ED1000;
  }
}

.content {
  width: 1160px;
  margin: 0 auto;
  h1,h2,h3,h4,h5,h6{
    line-height: 1.3em;
  }

  .paragraph {
    color: #696871;
  }

  .heroSection,
  .aiResponsibilitySection,
  .biddingSection,
  .automationSection,
  .pricingSection {
    padding: 70px 0;
  }

  .heroText {
    text-align: left;

    h1 {
      font-weight: 600;
      margin-bottom: 16px;
    }

    p {
      font-size: 16px;
      margin-bottom: 16px;
    }
  }

  .heroButton {
    height: 50px;
    padding: 0 40px;
    font-size: 18px;
  }

  .featuresSection {
    padding: 10px 0 50px 0;

    .featureCard {
      text-align: center;

      .featureContent {
        padding: 0 24px;

        .paragraph {
          font-size: 16px;
        }
      }

      .featureImage {
        height: 80px;
        margin-bottom: 24px;
      }

      h4 {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }

  .aiResponsibilitySection,
  .biddingSection,
  .automationSection {
    .sectionText {
      text-align: left;

      h2 {
        font-size: 36px;
        font-weight: 600;
        margin-bottom: 24px;
      }

      .paragraph {
        font-size: 18px;
      }

      p {
        font-size: 16px;
        margin-bottom: 24px;
      }

      ul {
        list-style: disc;
        padding: 0;
        padding-left: 1em;

        li {
          margin-bottom: 16px;
        }
      }

      .learnMore {
        color: #ED1000;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }

  .pricingSection {
    .priceCard {
      border-radius: 8px;
      padding-top: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: column;
      height: 100%;
      transition: all 0.3s ease, background-color 0.3s ease;
      background-color: #F8F8F8;
      text-align: center;

      h4 {
        font-size: 20px;
        font-weight: 600;
      }

      .price {
        font-size: 36px;
        font-weight: 600;
        margin: 16px 0;
        color: #333;
      }

      .featureList {
        list-style: none;
        padding: 0;
        margin: 16px 0;
        display: flex;
        flex-direction: column;
        align-items: center;

        li {
          margin-bottom: 16px;
          color: #696871;

          .anticon {
            color: #ED1000;
            margin-right: 8px;
          }
        }
      }

      .priceButton {
        height: 40px;
        font-size: 16px;
        color: #ED1000;
      }

      &:hover {
        transform: translateY(-16px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        background-color: #ED1000;
        color: white;

        h4,
        .price,
        .anticon,
        li {
          color: white;
        }

        .priceButton {
          background-color: #F59992;
          border-color: white;
          color: white;

          &:hover {
            background: white;
            color: #ED1000;
          }
        }
      }

      &.featuredCard {
        transform: translateY(-16px);
        background-color: #ED1000;
        color: white;

        h4,
        .price,
        .anticon,
        li {
          color: white;
        }

        .priceButton {
          background-color: #F59992;
          border-color: #F59992;
          color: white;

          &:hover {
            background: white;
            color: #ED1000;
          }
        }
      }
    }
  }
}