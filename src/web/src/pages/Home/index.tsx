import React, { useState } from 'react';
import { <PERSON>, Col, But<PERSON>, Card, Typography } from 'antd';
import styles from './style.module.less';
import logo from '../../../public/logo.svg';
import { Link } from '@umijs/max';
import { loginPath, registerPath } from '../../../config/routes';
import { sourceImageUrl } from '@/utils/common';
import { Footer } from '@/components';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [selectedCard, setSelectedCard] = useState<number>(1);

  return (
    <div className={styles.homePage}>
      {/* 头部导航 */}
      <div className={styles.header}>
        <Row justify="space-between" align="middle" style={{ padding: '0 40px', height: '64px' }}>
          <Col>
            <div className={styles.logo} onClick={() => window.scrollTo(0, 0)} style={{ cursor: 'pointer' }}>
              <img src={logo} alt="FlyPower.AI" height={40} />
            </div>
          </Col>
          <Col>
            <Row gutter={32} align="middle">
              <Col>
                <Link to={registerPath}>
                  <Button type="primary">
                    立即注册
                  </Button>
                </Link>
              </Col>
              <Col>
                <Link to={loginPath} className={styles.loginLink}>登录</Link>
              </Col>
            </Row>
          </Col>
        </Row>
      </div>

      <div className={styles.content}>
        {/* Hero Section */}
        <div className={styles.heroSection}>
          <Row align="middle" justify="center">
            <Col lg={12} md={24}>
              <div className={styles.heroText}>
                <Title style={{ fontSize: 50 }} level={1}>增长<br />由AI驱动</Title>
                <Paragraph className={styles.paragraph} style={{ fontSize: 20, paddingRight: '1em' }}>FlyPower AI广告引擎，为亚马逊卖家注入全新生产力，一站式提升全链路、多渠道运营效率，驱动业务持续增长。</Paragraph>
                <Link to={registerPath}><Button type="primary" size="large" className={styles.heroButton}>立即使用</Button></Link>
              </div>
            </Col>
            <Col lg={12} md={24}>
              <img src={sourceImageUrl('home/hero-section.svg')} alt="AI Driven Growth" style={{ maxWidth: '100%' }} />
            </Col>
          </Row>
        </div>

        {/* Features Section */}
        <div className={styles.featuresSection} >
          <Row gutter={[48, 48]} justify="center">
            <Col md={8} sm={24} className={styles.featureCard}>
              <div className={styles.featureContent}>
                <img src={sourceImageUrl('home/feature-intelligent-engine.svg')} alt="智能技术引擎" className={styles.featureImage} />
                <Title level={4}>智能技术引擎<br />驱动广告自动化盈利</Title>
                <Paragraph className={styles.paragraph}>AI智能调优与人机协同（AI Copilot）无缝结合，智能创建广告活动、预算、出价等方案，让您的投放策略、结构如流水般丝滑。</Paragraph>
              </div>
            </Col>
            <Col md={8} sm={24} className={styles.featureCard}>
              <div className={styles.featureContent}>
                <img src={sourceImageUrl('home/feature-attribution-model.svg')} alt="科学归因模型" className={styles.featureImage} />
                <Title level={4}>科学归因模型<br />让每一分钱都清晰可见</Title>
                <Paragraph className={styles.paragraph}>我们持续优化归因算法模型，能够精准评估每一次广告活动的贡献度，然后自动调整资源投入，将预算倾向于高产出广告活动，最大化您的广告ROI。</Paragraph>
              </div>
            </Col>
            <Col md={8} sm={24} className={styles.featureCard}>
              <div className={styles.featureContent}>
                <img src={sourceImageUrl('home/feature-market-insight.svg')} alt="全景市场洞察" className={styles.featureImage} />
                <Title level={4}>全景市场洞察<br />精准锁定竞争优势</Title>
                <Paragraph className={styles.paragraph}>深入挖掘海量数据，实时洞察竞品动向、品牌声量和消费者需求，让您知己知彼，料敌于先，牢牢掌握市场竞争主动权。</Paragraph>
              </div>
            </Col>
          </Row>
        </div>

        {/* AI Responsibility Section */}
        <div className={styles.aiResponsibilitySection}>
          <Row align="middle" justify="center">
            <Col lg={11} md={24}>
              <img src={sourceImageUrl('home/ai-responsibility-section.svg')} alt="AI 负责执行" style={{ maxWidth: '100%' }} />
            </Col>
            <Col lg={13} md={24}>
              <div className={styles.sectionText}>
                <Title level={2}>AI负责执行<br />您只需掌控全局</Title>
                <Paragraph className={styles.paragraph}>别让AI取代您的经验，让它放大您的智慧。我们行业首创的人机协同模式。</Paragraph>
                <ul className={styles.paragraph}>
                  <li>经验赋能AI：您的运营策略指导AI，让机器决策更懂您的业务。</li>
                  <li>AI赋能大脑：在您不熟悉的领域，AI提供数据洞察，助您做出更自信的判断。</li>
                  <li style={{ listStyle: 'none' }}>这不是简单的自动化，而是将您的经验与AI能力相乘，让您从重复劳动中解放，聚焦于真正的商业增长。</li>
                </ul>
                <br />
                <Link to={registerPath} className={styles.learnMore}>了解更多</Link>
              </div>
            </Col>
          </Row>
        </div>

        {/* Bidding Section */}
        <div className={styles.biddingSection}>
          <Row align="middle" justify="center">
            <Col lg={13} md={24}>
              <div className={styles.sectionText}>
                <Title level={2}>告别人工猜测<br /> 让投放决策精准如手术刀</Title>
                <Paragraph className={styles.paragraph}>我们的AI引擎不仅拥有海量数据和尖端算法，更能深度理解您的营销目标。</Paragraph>
                <ul className={styles.paragraph}>
                  <li>动态预算分配：将预算实时倾斜给高效益的广告活动。</li>
                  <li>一站式管理：覆盖广告创建、优化到洞察分析的全流程。</li>
                  <li>智能竞价与定向：自动锁定高转化流量。</li>
                  <li style={{ listStyle: 'none' }}>FlyPower AI为您突破人效极限，让每一分钱都创造最大价值。</li>
                </ul>
                <br />
                <Link to={registerPath} className={styles.learnMore}>了解更多</Link>
              </div>
            </Col>
            <Col lg={11} md={24} style={{  textAlign: 'right' }}>
              <img src={sourceImageUrl('home/bidding-section.svg')} alt="投放决策" style={{ maxWidth: '100%' }} />
            </Col>
          </Row>
        </div>

        {/* Automation Section */}
        <div className={styles.automationSection}>
          <Row align="middle" justify="center">
            <Col lg={11} md={24}>
              <img src={sourceImageUrl('home/automation-section.svg')} alt="自动操盘手" style={{ maxWidth: '100%' }} />
            </Col>
            <Col lg={13} md={24}>
              <div className={styles.sectionText}>
                <Title level={2}>您的7x24小时自动操盘手<br />精准执行，决胜千里</Title>
                <Paragraph className={styles.paragraph}>还在手动调价，被对手偷袭？告别繁琐，让AI成为您的全天候运营专家</Paragraph>
                <ul className={styles.paragraph}>
                  <li>精准错位，寸土不让：AI以15分钟/次的高频自动调价，结合10大区域的真实环境验证（99.9%成功率），将您的广告位牢牢锁在黄金位置，稳定压制对手。</li>
                  <li>分时管控，预算花在刀刃上：独创分时模板，根据小时级出单量，自动调整预算与竞价。高峰期猛攻，低谷期节流，最大化您的资金效率和广告ROI。</li>
                </ul>
                <br />
                <Link to={registerPath} className={styles.learnMore}>了解更多</Link>
              </div>
            </Col>
          </Row>
        </div>

        {/* Pricing Section */}
        <div className={styles.pricingSection}>
          <Title level={1} style={{ textAlign: 'left', fontSize: 60, marginBottom: '80px' }}>开启增长，<br />定制专属方案</Title>
          <Row gutter={[16, 32]} justify="center">
            <Col lg={8} md={24} onMouseEnter={() => setHoveredCard(0)} onMouseLeave={() => setHoveredCard(null)} onClick={() => setSelectedCard(0)}>
              <Card className={`${styles.priceCard} ${hoveredCard === 0 || (hoveredCard === null && selectedCard === 0) ? styles.featuredCard : ''}`}>
                <Title level={4}>基础版</Title>
                <Title level={2} className={styles.price}>¥49,800/年</Title>
                <ul className={styles.featureList}>
                  <li>子账户数量: 10个</li>
                  <li>自动化规则上限: 300条</li>
                  <li>链接排名功能上限: 5条(1个邮箱)</li>
                  <li>关键词排名监控上限: 50个</li>
                </ul>
                <Link to={registerPath}><Button block className={styles.priceButton}>购买咨询</Button></Link>
              </Card>
            </Col>
            <Col lg={8} md={24} onMouseEnter={() => setHoveredCard(1)} onMouseLeave={() => setHoveredCard(null)} onClick={() => setSelectedCard(1)}>
              <Card className={`${styles.priceCard} ${hoveredCard === 1 || (hoveredCard === null && selectedCard === 1) ? styles.featuredCard : ''}`}>
                <Title level={4}>卓越版</Title>
                <Title level={2} className={styles.price}>¥129,800/年</Title>
                <ul className={styles.featureList}>
                  <li>子账户数量: 30个</li>
                  <li>自动化规则上限: 1000条</li>
                  <li>链接排名功能上限: 20条(5个邮箱)</li>
                  <li>关键词排名监控上限: 400个</li>
                </ul>
                <Link to={registerPath}><Button block className={styles.priceButton}>购买咨询</Button></Link>
              </Card>
            </Col>
            <Col lg={8} md={24} onMouseEnter={() => setHoveredCard(2)} onMouseLeave={() => setHoveredCard(null)} onClick={() => setSelectedCard(2)}>
              <Card className={`${styles.priceCard} ${hoveredCard === 2 || (hoveredCard === null && selectedCard === 2) ? styles.featuredCard : ''}`}>
                <Title level={4}>企业版</Title>
                <Title level={2} className={styles.price}>¥1,880,000/年</Title>
                <ul className={styles.featureList}>
                  <li>子账户数量: 60个</li>
                  <li>自动化规则上限: 无</li>
                  <li>链接排名功能上限: 80条(10个邮箱)</li>
                  <li>关键词排名监控上限: 1000个</li>
                  <li>AMC Hub自定义分析模型</li>
                </ul>
                <Link to={registerPath}><Button block className={styles.priceButton}>购买咨询</Button></Link>
              </Card>
            </Col>
          </Row>
        </div>
      </div>
      <Footer/>
    </div>
  );
};

export default HomePage;
