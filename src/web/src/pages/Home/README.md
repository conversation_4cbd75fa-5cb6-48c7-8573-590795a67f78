# 首页组件

## 概述
这是一个营销型的首页组件，按照设计图实现，包含以下主要部分：

## 功能模块

### 1. 头部导航 (Header)
- Logo区域（使用占位符）
- 导航菜单：首页、产品、解决方案、定价
- 联系我们按钮
- 语言切换

### 2. 主要内容区域 (Hero Section)
- 主标题："增长由AI驱动"
- 产品描述
- 行动按钮："立即开始"
- 主图展示区域（使用占位符）

### 3. 功能特色区域 (Features Section)
- 智能投放系统
- 实时数据分析
- 全渠道覆盖
- 每个特色都有图标占位符和详细描述

### 4. AI优势展示区域 (AI Advantage Section)
- 标题："AI负责执行，您只需要运营全局"
- 三个主要优势点，每个都有勾选图标
- "了解更多"按钮
- 配图区域（使用占位符）

### 5. 告别人工测试区域 (Automation Section)
- 标题："告别人工测试，让我们来帮您加知手术刀"
- 三个自动化特性介绍
- "了解更多"按钮
- 配图区域（使用占位符）

### 6. 24小时自动助手区域 (Assistant Section)
- 标题："您的7x24小时自动操盘手，精准执行，决胜千里"
- 详细描述和三个特性点
- "了解更多"按钮
- 配图区域（使用占位符）

### 7. 价格方案区域 (Pricing Section)
- 标题："Get the right plan for future product."
- 三个价格方案：基础版、专业版、企业版
- 每个方案包含价格、描述和功能列表
- 专业版为推荐方案（高亮显示）

## 技术实现

### 组件结构
- 使用 React + TypeScript
- 采用 Ant Design 的 Row/Col 布局系统
- 响应式设计，支持移动端适配

### 样式特点
- 使用 Less 预处理器
- 渐变背景和现代化设计风格
- 悬停效果和过渡动画
- 完整的响应式布局

### 路由配置
- 路径：`/home`
- 设置为默认首页（根路径重定向到 `/home`）
- 无权限限制，任何用户都可以访问

## 占位符说明
所有图片和图标区域都使用了占位符，包括：
- Logo区域
- 主图展示
- 功能特色图标
- AI优势配图
- 自动化功能配图
- 助手功能配图

这些占位符可以在后期替换为实际的图片和图标。

## 使用方法
1. 访问 `http://localhost:8001` 即可查看首页
2. 页面完全响应式，支持桌面端和移动端
3. 所有按钮和链接都已设置，可以根据需要添加实际的跳转逻辑

## 注意事项
- 当前所有链接都是占位符（`href="#"`）
- 图片区域使用CSS渐变作为占位符
- 价格信息按照设计图设置，可根据实际需求调整
