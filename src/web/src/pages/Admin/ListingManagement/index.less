.asinInputFixed {
  padding: 0 10px !important;
}
.linkItem {
  color: #4E5969;
  &:hover {
    color: #ED1000;
    text-decoration: underline;
  }
}
.amazonIcon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 18px;
  height: 18px;
  border: 1.5px solid #000;
  color: #000;
  border-radius: 50%;
  transition: none;
  &:hover {
    color: #ED1000;
    border-color: #ED1000;
  }
}