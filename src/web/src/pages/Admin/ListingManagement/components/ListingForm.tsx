import React, { useState, useRef } from 'react';
import { Form, Input, Radio, DatePicker, Button, Row, Col, Select, Rate, Card } from 'antd';
import { PlusOutlined, MinusCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { getSiteCurrency } from '@/utils/common';
import dayjs from 'dayjs';
import type { TextAreaRef } from 'antd/es/input/TextArea';
import styles from './index.module.less';
import { useModel } from '@umijs/max';
import { getCountryTimezone } from '@/utils/bus';
import { AiOptionList } from '@/utils/common';
interface ListingFormProps {
  form: any;
  product?: {
    asin: string;
    title: string;
    price: number;
    rating: number;
    reviewCount: number;
    imageUrl: string;
    country: string;
    url: string;
    storeName: string;
  };
  hasPromotions: boolean;
  onPromotionChange: (value: boolean) => void;
  showProductInfo?: boolean;
  initialValues?: any;
}

const ListingForm: React.FC<ListingFormProps> = ({
  form,
  product,
  hasPromotions,
  onPromotionChange,
  showProductInfo = true,
  initialValues
}) => {
  const [showAiOptions, setShowAiOptions] = useState(false);
  const aiTextAreaRef = useRef<TextAreaRef>(null);
  const { productInfo } = useModel('productInfo');

  const PromotionList = [{
    name: '直接降价',
    value: '直接降价'
  }, {
    name: '优惠券',
    value: '优惠券'
  }, {
    name: 'Prime会员折扣',
    value: 'Prime会员折扣'
  }, {
    name: 'Lightning Deal',
    value: 'Lightning Deal'
  }, {
    name: 'Best Deal',
    value: 'Best Deal'
  }];

  const InventoryList = [{
    name: '库存充足：没有断货风险',
    value: 'normal'
  }, {
    name: '库存不足：需降低广告预算以保护库存',
    value: 'low'
  }, {
    name: '库存严重不足：即将断货，需暂停广告',
    value: 'critical'
  }];

  return (
    <Form
      form={form}
      layout="vertical"
      className={styles.addListingForm}
      initialValues={initialValues}
    >
      <Row gutter={24}>
        <Col span={12} style={{ position: 'relative' }}>
          {/* AI指令 */}
          <div className={styles.colTitle}>
            <span>AI指令</span>
            <QuestionCircleOutlined className={styles.questionIcon} />
          </div>
          <Form.Item
            // label="AI指令"
            name="ai_instruction"
            rules={[{ required: true, message: '请输入AI指令' }]}
          >
            <Input.TextArea
              ref={aiTextAreaRef}
              onFocus={() => setShowAiOptions(true)}
              onBlur={() => setShowAiOptions(false)}
              style={{height: '500px', resize: 'none', paddingBottom: 50}}
              placeholder="请输入你想要给AI的一些建议/指令，AI将根据您的指令制定广告策略。
例如：
广告目标：
• 近3个月广告目标：提高利润并稳定BSR排名，增加自然订单
• ACoS控制在30%以内
• 最大化大促期间的销量和曝光
• 积累更多高质量客户评论
广告参数设置：
• 日预算范围：$150~$200
• 每周/日预算调整比例：5%~10%
• 关键周/日预算调整比例：10%~20%
• 每周/日竞价调整比例：5%~10%
• 关键周/日竞价调整比例：10%~20%
• 搜索结果顶部竞价调整比例：0%~50%
• 详情页竞价调整比例：0%~50%
• 其他位置竞价调整比例：0%~50%
• 分时竞价调整比例：10%~20%
对标竞品：
• ASIN:
• ASIN:"
            />
          </Form.Item>
          {showAiOptions && (
            <div
              style={{
                position: 'absolute',
                left: 16,
                right: 16,
                bottom: 26,
                zIndex: 10,
                padding: 8,
                borderTop: '1px solid #f0f0f0',
                backgroundColor: '#fff',
                display: 'flex',
                gap: 8,
              }}
              onMouseDown={e => e.preventDefault()}
            >
              {Object.keys(AiOptionList).map(key => (
                <Button
                  key={key}
                  color="default"
                  variant="filled"
                  size="small"
                  onClick={() => {
                     const currentValue = form.getFieldValue('ai_instruction') || '';
                     const newValue = currentValue + (currentValue ? '\n\n' : '') + AiOptionList[key as keyof typeof AiOptionList].value;
                     form.setFieldsValue({ ai_instruction: newValue });
                     setTimeout(() => aiTextAreaRef.current?.focus({ cursor: 'end' }), 0);
                   }}
                >
                  {AiOptionList[key as keyof typeof AiOptionList].name}
                </Button>
              ))}
            </div>
          )}
        </Col>
        <Col span={12}>
          {/* 产品信息展示 */}
          <div className={styles.colTitle}>
            <span>相关信息</span>
            <QuestionCircleOutlined className={styles.questionIcon} />
          </div>
          <Card className={styles.productCard}>
            {showProductInfo && product && (
              <div className={styles.productInfo}>
                <img
                  src={product.imageUrl}
                  alt={product.title}
                  style={{
                    width: '80px',
                    height: '80px',
                    objectFit: 'contain',
                    border: '1px solid #f0f0f0',
                    borderRadius: '4px'
                  }}
                />
                <div style={{ marginLeft: '15px', flex: 1 }}>
                  <div style={{ margin: 0, color: '#1890ff' }}>
                    <a href={product.url} target="_blank" rel="noopener noreferrer" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>{product.title}</a>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: '15px' }}>
                    <div>ASIN:{product.asin}</div>
                    <div>价格: {getSiteCurrency(product.country)}{product.price}</div>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Rate allowHalf style={{ fontSize: 14 }} disabled value={product.rating} />
                      <span style={{ marginLeft: 8, fontSize: 12, fontWeight: 'bold' }}>{product.rating}({product.reviewCount}条评价)</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {/* 库存情况 */}
            <div className={styles.formCard}>
              <Form.Item
                className={styles.formItem}
                label="库存情况"
                required={false}
                name="inventory_status"
                rules={[{ required: true, message: '请输入库存情况' }]}
              >
                <Radio.Group style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                  {InventoryList.map(item => (
                    <Radio key={item.value} value={item.value}>{item.name}</Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </div>
            <div className={styles.formCard}>
              {/* 促销计划 */}
              <Form.Item
                className={styles.formItem}
                label="促销计划"
                required={false}
                name={["promotion_plan", "has_promotion"]}
                rules={[{ required: true, message: '请选择是否有促销计划' }]}
                initialValue={false}
              >
                <Radio.Group 
                  value={hasPromotions} 
                  onChange={e => {
                    onPromotionChange(e.target.value);
                    // 如果从无促销切换到有促销，确保有一个默认的促销计划
                    if (e.target.value === true) {
                      const currentPromotions = form.getFieldValue(['promotion_plan', 'promotion_list']) || [];
                      if (currentPromotions.length === 0) {
                        form.setFieldsValue({
                          promotion_plan: {
                            promotion_list: [{ id: '' }] // 新增时明确设置id为空字符串
                          }
                        });
                      }
                    }
                  }}
                >
                  <Radio value={false}>无促销</Radio>
                  <Radio value={true}>有促销</Radio>
                </Radio.Group>
              </Form.Item>
            

            {/* 当选择有促销时显示的促销计划列表 */}
            {hasPromotions === true && (
              <Form.List
                name={["promotion_plan", "promotion_list"]}
                initialValue={[{ id: '' }]} // 初始促销计划设置空id
              >
                {(fields, { add, remove }, { errors }) => (
                  <>
                    {fields.map((field, index) => (
                      <div key={field.key} className={styles.promotionCard}>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <div style={{ fontWeight: 'bold' }}>促销 #{index + 1}</div>
                          <Button
                            type="text"
                            onClick={() => {
                              remove(field.name);
                              // 如果删除后没有促销计划了，自动切换到无促销
                              if (fields.length === 1) {
                                onPromotionChange(false);
                                form.setFieldsValue({
                                  promotion_plan: {
                                    has_promotion: false,
                                    promotion_list: []
                                  }
                                });
                              }
                            }}
                            icon={<MinusCircleOutlined style={{ color: '#ff4d4f' }} />}
                          />
                        </div>
                        
                        {/* 隐藏的id字段，保存促销计划ID */}
                        <Form.Item
                          {...field}
                          name={[field.name, "id"]}
                          hidden={true}
                        >
                          <Input />
                        </Form.Item>

                        <Form.Item
                          {...field}
                          label="促销日期"
                          name={[field.name, "time_range"]}
                          rules={[{ required: true, message: '请选择促销日期' }]}
                          getValueProps={(value) => {
                            if (Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
                              return { value: [dayjs(value[0]), dayjs(value[1])] };
                            }
                            return { value };
                          }}
                          getValueFromEvent={(dates) => {
                            if (Array.isArray(dates) && dates.length === 2 && dates[0] && dates[1]) {
                              return [dates[0].format('YYYY-MM-DD'), dates[1].format('YYYY-MM-DD')];
                            }
                            return dates;
                          }}
                        >
                          <DatePicker.RangePicker
                            style={{ width: '100%' }}
                            placeholder={['开始日期', '结束日期']}
                            format="YYYY-MM-DD"
                            disabledDate={(current) => {
                              // 禁用今天之前的日期
                              return current && current < dayjs(getCountryTimezone(productInfo?.country || 'us')).startOf('day');
                            }}
                          />
                        </Form.Item>

                        <Row gutter={16}>
                          <Col span={12}>
                            <Form.Item
                              {...field}
                              label="促销类型"
                              name={[field.name, "promotion_type"]}
                              rules={[{ required: true, message: '请选择促销类型' }]}
                            >
                              <Select placeholder="请选择">
                                {PromotionList.map(item => (
                                  <Select.Option key={item.value} value={item.value}>{item.name}</Select.Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...field}
                              label={`最终折扣价(${product ? getSiteCurrency(product.country) : '$'})`}
                              name={[field.name, "discount_price"]}
                              rules={[{ required: true, message: '请输入最终折扣价' }]}
                            >
                              <Input placeholder="请输入" />
                            </Form.Item>
                          </Col>
                        </Row>
                      </div>
                    ))}

                    <Form.Item>
                      <Button
                        className={styles.addPromotionButton}
                        onClick={() => add({ id: '' })} // 新增时明确设置id为空字符串
                        icon={<PlusOutlined />}
                        style={{ width: '100%' }}
                      >
                        添加促销计划
                      </Button>
                      <Form.ErrorList errors={errors} />
                    </Form.Item>
                  </>
                )}
              </Form.List>
            )}
            </div>
          </Card>
        </Col>
      </Row>
    </Form>
  );
};

export default ListingForm;
