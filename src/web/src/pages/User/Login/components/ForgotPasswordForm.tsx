import {
  LockOutlined,
  MailOutlined,
  PhoneOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons';
import { useIntl, FormattedMessage } from '@umijs/max';
import { Alert, message, Form, Input, Button, Space } from 'antd';
import React, { useState } from 'react';
import { getDynamicCode } from '@/services/ibidder_api/user';
import styles from '../index.less';

const ForgotPasswordMessage: React.FC<{
  content: string;
}> = ({ content }) => {
  return (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );
};

interface ForgotPasswordFormProps {
  onBackToLogin: () => void;
}

const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({ onBackToLogin }) => {
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [verificationMethod, setVerificationMethod] = useState<'sms' | 'email'>('sms');
  const [captchaCountDown, setCaptchaCountDown] = useState<number>(0);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [userAccount, setUserAccount] = useState<string>('');
  const intl = useIntl();
  const [form] = Form.useForm();

  // 验证是否为邮箱格式
  const isEmail = (value: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value);
  };

  // 验证是否为手机号格式（支持中国大陆手机号）
  const isPhone = (value: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(value);
  };

  // 获取验证码
  const handleGetCaptcha = async (values: { account: string }) => {
    try {
      const params: API.GetDynamicCodeParams = {};

      if (verificationMethod === 'email') {
        if (!isEmail(values.account)) {
          setErrorMessage(intl.formatMessage({
            id: 'pages.forgotPassword.email.invalid',
            defaultMessage: '请输入有效的邮箱地址！',
          }));
          return;
        }
        params.email = values.account;
      } else {
        if (!isPhone(values.account)) {
          setErrorMessage(intl.formatMessage({
            id: 'pages.forgotPassword.phone.invalid',
            defaultMessage: '请输入有效的手机号！',
          }));
          return;
        }
        params.phone = values.account;
      }

      const res = await getDynamicCode(params) as any;

      if (res.code === 200) {
        message.success(intl.formatMessage({
          id: 'pages.forgotPassword.getCaptcha.success',
          defaultMessage: '验证码发送成功！',
        }));

        setUserAccount(values.account);
        setCurrentStep(1);
        setErrorMessage('');

        // 启动倒计时
        setCaptchaCountDown(60);
        const timer = setInterval(() => {
          setCaptchaCountDown((prevCount) => {
            if (prevCount <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prevCount - 1;
          });
        }, 1000);
      } else {
        setErrorMessage(res.message || intl.formatMessage({
          id: 'pages.forgotPassword.getCaptcha.failed',
          defaultMessage: '验证码发送失败，请重试！',
        }));
      }
    } catch (error) {
      console.error('获取验证码失败:', error);
      setErrorMessage(intl.formatMessage({
        id: 'pages.forgotPassword.getCaptcha.failed',
        defaultMessage: '验证码发送失败，请重试！',
      }));
    }
  };

  // 验证验证码
  const handleVerifyCode = async (values: { account: string; captcha: string }) => {
    try {
      // 这里应该调用验证码验证接口
      // const res = await verifyDynamicCode({ ... });

      // 模拟验证成功
      message.success(intl.formatMessage({
        id: 'pages.forgotPassword.verify.success',
        defaultMessage: '验证成功！',
      }));
      setCurrentStep(2);
      setErrorMessage('');
      form.resetFields();
    } catch (error) {
      setErrorMessage(intl.formatMessage({
        id: 'pages.forgotPassword.verify.failed',
        defaultMessage: '验证码错误，请重试！',
      }));
    }
  };

  // 重置密码
  const handleResetPassword = async (values: { password: string; confirmPassword: string }) => {
    if (values.password !== values.confirmPassword) {
      setErrorMessage(intl.formatMessage({
        id: 'pages.forgotPassword.password.mismatch',
        defaultMessage: '两次输入的密码不一致！',
      }));
      return;
    }

    try {
      // 这里应该调用重置密码接口
      // const res = await resetPassword({ ... });

      // 模拟重置成功
      message.success(intl.formatMessage({
        id: 'pages.forgotPassword.reset.success',
        defaultMessage: '密码重置成功！请使用新密码登录。',
      }));

      // 返回登录页面
      setTimeout(() => {
        onBackToLogin();
      }, 1500);
    } catch (error) {
      setErrorMessage(intl.formatMessage({
        id: 'pages.forgotPassword.reset.failed',
        defaultMessage: '密码重置失败，请重试！',
      }));
    }
  };

  // 重新发送验证码
  const handleResendCaptcha = async () => {
    try {
      const params: API.GetDynamicCodeParams = {};

      if (verificationMethod === 'email') {
        params.email = userAccount;
      } else {
        params.phone = userAccount;
      }

      const res = await getDynamicCode(params) as any;

      if (res.code === 200) {
        message.success(intl.formatMessage({
          id: 'pages.forgotPassword.getCaptcha.success',
          defaultMessage: '验证码发送成功！',
        }));

        setCaptchaCountDown(60);
        const timer = setInterval(() => {
          setCaptchaCountDown((prevCount) => {
            if (prevCount <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prevCount - 1;
          });
        }, 1000);
      } else {
        message.error(res.message || intl.formatMessage({
          id: 'pages.forgotPassword.getCaptcha.failed',
          defaultMessage: '验证码发送失败，请重试！',
        }));
      }
    } catch (error) {
      message.error(intl.formatMessage({
        id: 'pages.forgotPassword.getCaptcha.failed',
        defaultMessage: '验证码发送失败，请重试！',
      }));
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        // 选择验证方式
        return (
          <div className={styles.forgotPasswordContainer}>
            <div className={styles.forgotPasswordHeader}>
              <div className={styles.forgotPasswordTitle}>
                <FormattedMessage id="pages.forgotPassword.resetTitle" defaultMessage="重置密码" />
              </div>
            </div>

            <div className={styles.verificationMethodButtons}>
              <Button
                type="primary"
                size="large"
                block
                className={styles.methodButton}
                onClick={() => {
                  setVerificationMethod('sms');
                  setCurrentStep(1);
                }}
              >
                <FormattedMessage id="pages.forgotPassword.smsVerification" defaultMessage="短信验证（真实手机号注册）" />
              </Button>

              <Button
                type="primary"
                size="large"
                block
                className={styles.methodButton}
                onClick={() => {
                  setVerificationMethod('email');
                  setCurrentStep(1);
                }}
              >
                <FormattedMessage id="pages.forgotPassword.emailVerification" defaultMessage="邮箱验证" />
              </Button>
            </div>

            <div className={styles.backToLoginContainer}>
              <Button type="link" onClick={onBackToLogin} className={styles.backToLoginLink}>
                <FormattedMessage id="pages.forgotPassword.backToLogin" defaultMessage="返回登录页面" />
              </Button>
            </div>
          </div>
        );

      case 1:
        // 账号和验证码输入
        return (
          <div className={styles.forgotPasswordContainer}>

            <div className={styles.forgotPasswordHeader}>
              <div className={styles.forgotPasswordTitle}>
                <FormattedMessage id="pages.forgotPassword.title" defaultMessage="忘记密码" />
                {verificationMethod === 'sms' ? '' : '？'}
              </div>
            </div>

            <Form
              form={form}
              onFinish={handleVerifyCode}
              className={styles.forgotPasswordForm}
            >
              <Form.Item
                name="account"
                rules={[
                  {
                    required: true,
                    message: verificationMethod === 'email'
                      ? intl.formatMessage({ id: 'pages.forgotPassword.email.required', defaultMessage: '请输入邮箱地址！' })
                      : intl.formatMessage({ id: 'pages.forgotPassword.phone.required', defaultMessage: '请输入手机号！' }),
                  },
                ]}
              >
                <Input
                  prefix={verificationMethod === 'email' ? <MailOutlined /> : <PhoneOutlined />}
                  placeholder={verificationMethod === 'email'
                    ? intl.formatMessage({ id: 'pages.forgotPassword.email.placeholder', defaultMessage: '请输入邮箱地址' })
                    : intl.formatMessage({ id: 'pages.forgotPassword.phone.placeholder', defaultMessage: '请输入手机号' })
                  }
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="captcha"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'pages.forgotPassword.captcha.required', defaultMessage: '请输入验证码！' }),
                  },
                ]}
              >
                <Space.Compact style={{ width: '100%' }}>
                  <Input
                    placeholder={intl.formatMessage({ id: 'pages.forgotPassword.captcha.placeholder', defaultMessage: '请输入验证码' })}
                    size="large"
                    maxLength={6}
                    className={styles.captchaInput}
                  />
                  <Button
                    type="primary"
                    size="large"
                    className={styles.getCaptchaBtn}
                    disabled={captchaCountDown > 0}
                    onClick={async () => {
                      const accountValue = form.getFieldValue('account');
                      if (accountValue) {
                        await handleGetCaptcha({ account: accountValue });
                      } else {
                        form.validateFields(['account']).catch(() => { });
                      }
                    }}
                  >
                    {captchaCountDown > 0 ? `${captchaCountDown}s` : intl.formatMessage({ id: 'pages.forgotPassword.getCaptchaBtn', defaultMessage: '获取验证码' })}
                  </Button>
                </Space.Compact>
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" size="large" block className={styles.resetPasswordButton}>
                  <FormattedMessage id="pages.forgotPassword.resetPasswordBtn" defaultMessage="重置密码" />
                </Button>
              </Form.Item>
            </Form>

            <div className={styles.backToLoginContainer}>
              <Button type="link" onClick={onBackToLogin} className={styles.backToLoginLink}>
                <FormattedMessage id="pages.forgotPassword.backToLogin" defaultMessage="返回登录页面" />
              </Button>
            </div>
          </div>
        );

      case 2:
        // 设置新密码
        return (
          <div className={styles.forgotPasswordContainer}>
            <div className={styles.forgotPasswordHeader}>
              <Button
                type="text"
                icon={<ArrowLeftOutlined />}
                onClick={onBackToLogin}
                className={styles.backButton}
              >
                <FormattedMessage id="pages.forgotPassword.backToLogin" defaultMessage="返回登录页面" />
              </Button>
              <div className={styles.forgotPasswordTitle}>
                <FormattedMessage id="pages.forgotPassword.resetTitle" defaultMessage="重置密码" />
              </div>
            </div>

            <Form
              form={form}
              onFinish={handleResetPassword}
              className={styles.forgotPasswordForm}
            >
              <Form.Item
                name="password"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'pages.forgotPassword.newPassword.required', defaultMessage: '请输入新密码！' }),
                  },
                  {
                    min: 6,
                    message: intl.formatMessage({ id: 'pages.forgotPassword.newPassword.minLength', defaultMessage: '密码至少6位！' }),
                  },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder={intl.formatMessage({ id: 'pages.forgotPassword.newPasswordPlaceholder', defaultMessage: '新密码' })}
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="confirmPassword"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({ id: 'pages.forgotPassword.confirmPassword.required', defaultMessage: '请确认新密码！' }),
                  },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder={intl.formatMessage({ id: 'pages.forgotPassword.confirmPasswordPlaceholder', defaultMessage: '新密码二次确认' })}
                  size="large"
                />
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" size="large" block className={styles.confirmButton}>
                  <FormattedMessage id="pages.forgotPassword.confirmBtn" defaultMessage="确认" />
                </Button>
              </Form.Item>
            </Form>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={styles.loginFormContainer}>
      {errorMessage && <ForgotPasswordMessage content={errorMessage} />}
      {renderStepContent()}
    </div>
  );
};

export default ForgotPasswordForm; 