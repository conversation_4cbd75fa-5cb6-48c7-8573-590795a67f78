import {
  QuestionOutlined,
} from '@ant-design/icons';
import { useIntl, Helmet } from '@umijs/max';
import { Tooltip } from 'antd';
import Settings from '../../../../config/defaultSettings';
import React, { useState } from 'react';
import LoginForm from './components/LoginForm';
import ForgotPasswordForm from './components/ForgotPasswordForm';
import styles from './index.less';
import { Footer } from '@/components';

// const Lang = () => {
//   return (
//     <div className={styles.lang} data-lang>
//       {SelectLang && <SelectLang />}
//     </div>
//   );
// };

const Login: React.FC = () => {
  const [currentView, setCurrentView] = useState<'login' | 'forgotPassword'>('login');
  const intl = useIntl();

  const handleShowForgotPassword = () => {
    setCurrentView('forgotPassword');
  };

  const handleBackToLogin = () => {
    setCurrentView('login');
  };

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          {intl.formatMessage({
            id: 'menu.login',
            defaultMessage: '登录页',
          })}
          - {Settings.title}
        </title>
      </Helmet>

      <div className={styles.leftPanel}>
        <img src="/images/logo-white.svg" alt="" height={36} style={{position: 'absolute', top: '5vh', left: '5vh'}}/>
        <div className={styles.dashboardPreview}>
          <img
            src="/images/login-banner1.jpg"
            alt="Dashboard Preview"
            className={styles.previewImage}
          />
        </div>
        <div className={styles.leftContent}>
          <div className={styles.leftTitle}>FlyPower 您的智能广告加速器</div>
          <div className={styles.leftSubtitle}>AI驱动的预测性分析，洞悉购买先机，让每一分广告投入都更具价值。</div>
        </div>
      </div>

      <div className={styles.rightPanel}>
        {/* <Lang /> */}
        {currentView === 'login' ? (
          <LoginForm onForgotPassword={handleShowForgotPassword} />
        ) : (
          <ForgotPasswordForm onBackToLogin={handleBackToLogin} />
        )}

        <Footer style={{
          position: 'absolute',
          bottom: '20px',
          left: '20px'
        }} />
      </div>

      {/* 右下角问号浮窗 */}
      <Tooltip
        title={
          <div>
            <div style={{ marginBottom: 8, fontWeight: 'bold' }}>需要帮助？</div>
            <div style={{ marginBottom: 4 }}>• 忘记密码请联系管理员</div>
            {/* <div style={{ marginBottom: 4 }}>• 账号问题请发送邮件至 <EMAIL></div>
            <div>• 技术支持热线：400-123-4567</div> */}
          </div>
        }
        placement="topLeft"
        style={{ position: 'fixed', bottom: 20, left: 20, zIndex: 1000 }}
      >
        <div className={styles.helpFloat}>
          <QuestionOutlined />
        </div>
      </Tooltip>
    </div>
  );
};

export default Login;