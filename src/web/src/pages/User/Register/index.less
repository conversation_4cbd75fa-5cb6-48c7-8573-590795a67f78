.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #fde8e8 0%, #fff0fc 50%, #fdefe8 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px 20px 20px;
  overflow: hidden;
}

// 顶部logo
.topLogo {
  position: absolute;
  top: 40px;
  left: 40px;
  z-index: 10;
}

// 主标题
.mainTitle {
  font-size: 36px;
  font-weight: 600;
  color: #333333;
  text-align: center;
  margin-bottom: 60px;
  letter-spacing: 2px;
  z-index: 5;
  position: relative;
}

// 注册表单卡片
.registerCard {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 0;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 700px;
  z-index: 5;
  position: relative;
}

// 底部标语
.bottomSlogan {
  font-size: 16px;
  color: #666;
  text-align: center;
  margin-top: 40px;
  z-index: 5;
  position: relative;
}

// 装饰性图标
.decorativeIcons {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.iconTopRight {
  position: absolute;
  top: 10%;
  right: 10%;
  width: 200px;
  height: 200px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%234a90e2;stop-opacity:0.1" /><stop offset="100%" style="stop-color:%236bb6ff;stop-opacity:0.05" /></linearGradient></defs><circle cx="100" cy="100" r="80" fill="url(%23grad1)" stroke="%234a90e2" stroke-width="2" stroke-opacity="0.2"/><path d="M70 100 L90 120 L130 80" stroke="%234a90e2" stroke-width="3" stroke-opacity="0.3" fill="none"/></svg>') no-repeat center;
  background-size: contain;
  opacity: 0.6;
  animation: float 8s ease-in-out infinite;
}

.iconBottomLeft {
  position: absolute;
  bottom: 15%;
  left: 8%;
  width: 150px;
  height: 150px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 150 150"><defs><linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%236bb6ff;stop-opacity:0.08" /><stop offset="100%" style="stop-color:%234a90e2;stop-opacity:0.03" /></linearGradient></defs><rect x="20" y="20" width="110" height="110" rx="15" fill="url(%23grad2)" stroke="%236bb6ff" stroke-width="2" stroke-opacity="0.2"/><circle cx="75" cy="75" r="25" fill="none" stroke="%236bb6ff" stroke-width="2" stroke-opacity="0.3"/></svg>') no-repeat center;
  background-size: contain;
  opacity: 0.5;
  animation: float 10s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(2deg);
  }
}

// 注册表单样式
.registerFormContainer {
  padding: 40px;
  background: transparent;
}

.registerHeader {
  text-align: center;
  margin-bottom: 32px;
}

.registerTitle {
  font-size: 24px;
  color: #333;
  font-weight: 600;
  margin-bottom: 8px;
}

.registerSubtitle {
  font-size: 14px;
  color: #8c8c8c;
}

.registerForm {
  .ant-form-item {
    margin-bottom: 20px !important;
  }
}

.prefixIcon {
  color: #bfbfbf;
  margin-right: 8px;
  font-size: 16px;
}



.demoButton {
  width: 100%;
  height: 48px !important;
  font-size: 16px !important;
  font-weight: 600;
  border-radius: 8px;
  margin-top: 20px;
}

// 公司类型按钮组
.companyTypeButtons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.typeButton {
  height: 40px;
  border-radius: 8px;
  font-size: 14px;
}

// 广告支出按钮组
.adSpendButtons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.spendButton {
  height: 40px;
  border-radius: 8px;
  font-size: 14px;
}



// 全局样式覆盖
:global {
  .ant-form-item-label > label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
  }

  .ant-input-affix-wrapper {
    padding-top: 12px !important;
    padding-bottom: 12px !important;
    border-radius: 8px;
    border-color: #d9d9d9;

    .ant-input {
      font-size: 14px;
    }

    &:focus,
    &.ant-input-affix-wrapper-focused {
      border-color: #4285f4;
      box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
    }
  }

  .ant-input {
    border-radius: 8px;
    border-color: #d9d9d9;
    padding: 12px;

    &:focus,
    &:hover {
      border-color: #4285f4;
    }
  }

  .ant-form-item-explain-error {
    font-size: 12px;
    padding-top: 2px;
    color: #ff4d4f;
  }

  .ant-form-item-has-error {
    .ant-input-affix-wrapper {
      border-color: #ff4d4f !important;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
    }

    .ant-input {
      border-color: #ff4d4f !important;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 20px 15px;
  }

  .topLogo {
    top: 20px;
    left: 20px;
  }

  .logoText {
    font-size: 20px;
  }

  .mainTitle {
    font-size: 28px;
    margin-bottom: 40px;
  }

  .registerCard {
    max-width: 100%;
  }

  .registerFormContainer {
    padding: 30px 20px;
  }

  .footerContent {
    flex-direction: column;
    gap: 8px;
    text-align: center;

    span {
      white-space: normal;
    }
  }

  .iconTopRight {
    width: 120px;
    height: 120px;
    top: 5%;
    right: 5%;
  }

  .iconBottomLeft {
    width: 100px;
    height: 100px;
    bottom: 10%;
    left: 5%;
  }
}

@media (max-width: 480px) {
  .mainTitle {
    font-size: 24px;
    margin-bottom: 30px;
  }

  .registerFormContainer {
    padding: 25px 15px;
  }

  .footerContent {
    font-size: 11px;
  }
}
