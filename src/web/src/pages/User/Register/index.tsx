import { useIntl, Helm<PERSON>, <PERSON> } from '@umijs/max';
import Settings from '../../../../config/defaultSettings';
import React from 'react';
import styles from './index.less';
import RegisterForm from './components/RegisterForm';
import logo from '../../../../public/logo.svg';
import { Footer } from '@/components';

const Register: React.FC = () => {
  const intl = useIntl();

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          {intl.formatMessage({
            id: 'menu.register',
            defaultMessage: '注册页',
          })}
          - {Settings.title}
        </title>
      </Helmet>

      {/* 顶部logo */}
      <div className={styles.topLogo}>
        <Link to="/">
          <img src={logo} alt="FlyPower.AI" height={40} />
        </Link>
      </div>

      {/* 主标题 */}
      <div className={styles.mainTitle}>
        极致运营，AI驱动，无忧提升生意
      </div>

      {/* 注册表单卡片 */}
      <div className={styles.registerCard}>
        <RegisterForm />
      </div>

      {/* 底部标语 */}
      <div className={styles.bottomSlogan}>
        人机智汇，创新驱动增长
      </div>

      {/* 底部版权信息 */}
      <Footer style={{ marginTop: 8 }} />

      {/* 装饰性图标 */}
      <div className={styles.decorativeIcons}>
        <div className={styles.iconTopRight}></div>
        <div className={styles.iconBottomLeft}></div>
      </div>
    </div>
  );
};

export default Register;
