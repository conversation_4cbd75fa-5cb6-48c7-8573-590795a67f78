import React, { useEffect } from 'react';
import { Modal, Form, Select, Input, Button } from 'antd';

const { Option } = Select;
const { TextArea } = Input;

interface StrategyEditModalProps {
  open: boolean;
  onCancel: () => void;
  onSave: (values: any) => void;
  weekStrategyData: any;
}

const StrategyEditModal: React.FC<StrategyEditModalProps> = ({ open, onCancel, onSave, weekStrategyData }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (open && weekStrategyData) {
      form.setFieldsValue({
        strategy: weekStrategyData.approach || '',
        strategyReason: weekStrategyData.rationale || '',
        mainTarget: weekStrategyData.primary_goal?.goal || '',
        mainTargetReason: weekStrategyData.primary_goal?.rationale || '',
        specificTargets: (weekStrategyData.other_goals || []).join('\n')
      });
    }
  }, [open, weekStrategyData, form]);

  const handleSave = () => {
    form.validateFields().then(values => {
      onSave(values);
    });
  };

  return (
    <Modal
      title="本周广告投放策略调整"
      open={open}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSave}>
          保存
        </Button>,
      ]}
      width={800}
      maskClosable={false}
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          label="总体策略"
          name="strategy"
          rules={[{ required: true, message: '请选择总体策略' }]}
        >
          <Select>
            <Option value="aggressive">激进</Option>
            <Option value="balanced">平衡</Option>
            <Option value="conservative">保守</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="总体策略调整理由"
          name="strategyReason"
          rules={[{ required: true, message: '请输入总体策略调整理由' }]}
        >
          <TextArea rows={4} placeholder="请输入总体策略调整理由" />
        </Form.Item>

        <Form.Item
          label={<span style={{ color: '#ff4d4f' }}>*主要目标</span>}
          name="mainTarget"
          rules={[{ required: true, message: '请输入主要目标' }]}
        >
          <TextArea rows={3} placeholder="请输入主要目标" />
        </Form.Item>

        <Form.Item
          label="主要目标调整理由"
          name="mainTargetReason"
          rules={[{ required: true, message: '请输入主要目标调整理由' }]}
        >
          <TextArea rows={3} placeholder="请输入主要目标调整理由" />
        </Form.Item>

        <Form.Item
          label="具体目标"
          name="specificTargets"
          rules={[{ required: true, message: '请输入具体目标' }]}
        >
          <TextArea rows={6} placeholder="请输入具体目标" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default StrategyEditModal; 