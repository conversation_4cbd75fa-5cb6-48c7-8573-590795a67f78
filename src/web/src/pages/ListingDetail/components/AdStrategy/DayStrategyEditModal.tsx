import React from 'react';
import { Modal, Form, Input, Select, Button } from 'antd';

const { TextArea } = Input;

export interface DayStrategyEditModalProps {
  open: boolean;
  onCancel: () => void;
  onSave: (values: any) => void;
  initialValues?: {
    approach?: string;
    rationale?: string;
    day_budget?: {
      amount?: number;
      rationale?: string;
    };
  };
}

const approachOptions = [
  { label: '保守', value: 'conservative' },
  { label: '平衡', value: 'balanced' },
  { label: '激进', value: 'aggressive' },
];

const DayStrategyEditModal: React.FC<DayStrategyEditModalProps> = ({ open, onCancel, onSave, initialValues }) => {
  const [form] = Form.useForm();

  React.useEffect(() => {
    if (open) {
      form.resetFields();
      if (initialValues) {
        form.setFieldsValue({
          approach: initialValues.approach,
          rationale: initialValues.rationale,
          day_budget_amount: initialValues.day_budget?.amount,
          day_budget_rationale: initialValues.day_budget?.rationale,
        });
      }
    }
  }, [open, initialValues, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onSave({
        approach: values.approach,
        rationale: values.rationale,
        day_budget: {
          amount: values.day_budget_amount,
          rationale: values.day_budget_rationale,
        },
      });
      form.resetFields();
    } catch (e) {
      // 校验失败
    }
  };

  return (
    <Modal
      title="今日广告投放策略调整"
      open={open}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      width={600}
      footer={null}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          approach: initialValues?.approach,
          rationale: initialValues?.rationale,
          day_budget_amount: initialValues?.day_budget?.amount,
          day_budget_rationale: initialValues?.day_budget?.rationale,
        }}
      >
        <Form.Item
          label="总体策略"
          name="approach"
          rules={[{ required: true, message: '请选择总体策略' }]}
        >
          <Select options={approachOptions} placeholder="请选择" />
        </Form.Item>
        <Form.Item
          label="总体策略调整理由"
          name="rationale"
          rules={[{ required: true, message: '请输入总体策略调整理由' }]}
        >
          <TextArea rows={4} placeholder="请输入调整理由" />
        </Form.Item>
        <Form.Item
          label="今日预算($)"
          name="day_budget_amount"
          rules={[{ required: true, message: '请输入今日预算' }]}
        >
          <Input type="number" min={0} step={0.01} placeholder="请输入预算金额" />
        </Form.Item>
        <Form.Item
          label="今日预算调整理由"
          name="day_budget_rationale"
          rules={[{ required: true, message: '请输入今日预算调整理由' }]}
        >
          <TextArea rows={3} placeholder="请输入预算调整理由" />
        </Form.Item>
        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 12, marginTop: 24 }}>
          <Button onClick={() => { form.resetFields(); onCancel(); }}>取消</Button>
          <Button type="primary" onClick={handleOk}>保存</Button>
        </div>
      </Form>
    </Modal>
  );
};

export default DayStrategyEditModal; 