@import '~@/utils/common.less';

.container {
  background-color: #fff;
}

.viewSwitcher {
  margin-bottom: 16px;
}

.renderContent {
  min-height: 600px;
}

.title {
  margin-bottom: 24px;
  font-weight: bold;
  font-size: 18px;
}



// AI工作动态相关样式
.aiWorkContainer {
  background-color: #fff;
  border-radius: 8px;
}

.aiWorkTabs {
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
}

.aiWorkList {
  padding: 16px;
}

.aiWorkItem {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.aiAvatar {
  margin-right: 16px;
  background-color: #f0f0f0;
}

.aiWorkContent {
  flex: 1;
  display: flex;
  align-items: flex-start;
  flex-wrap: nowrap;
}

.aiWorkHeader {
  display: flex;
  margin-bottom: 8px;
}

.aiWorkType {
  font-weight: bold;
  color: #333;
  margin-top: 12px;
  margin-right: 16px;
  white-space: nowrap;
  width: 100px;
}

.aiWorkBaseOn {
  color: #8c8c8c;
}

.aiWorkMainContent {
  flex: 1;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
}

.aiWorkLink {
  color: #1890ff;
  margin-left: 8px;
  white-space: nowrap;
}

.aiWorkTimestamp {
  color: #8c8c8c;
  font-size: 12px;
  white-space: nowrap;
  margin-left: 16px;
  width: 150px;
  text-align: right;
}


.highlight {
  color: #1890ff;
  cursor: pointer;
}

.boldItem {
  font-weight: bold;
}

.renderContent {
  min-height: 650px;
}

.viewSwitcher {
  margin-bottom: 20px;
  margin-top: 10px;

  :global(.ant-segmented-item-label[aria-selected="true"]) {
    background-color: @primaryColor;
    border-radius: 4px;
    color: #fff;
  }
}

.reportGoToReview {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  align-items: center;
  padding-top: 30px;
}