import React, { useEffect, useState, useMemo } from 'react';
import styles from './styles.module.less';
import { Button, Space, Typography, Modal, Form, Row, Col, message, Card } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import { updateListing } from '@/services/ibidder_api/listings';
import PromotionInfo from './PromotionInfo';
import ListingForm from '@/pages/Admin/ListingManagement/components/ListingForm';
import { useModel } from '@umijs/max';
import { getResizedAmazonImageUrl } from '@/utils/common';
import { AiOptionList } from '@/utils/common';

// 定义促销计划类型
interface PromotionItem {
  time_range?: [string, string];
  promotion_type?: string;
  discount_price?: number | string;
  id?: string;
}

interface PromotionPlan {
  has_promotion: boolean;
  promotion_list?: PromotionItem[];
}

const { Text } = Typography;

interface ListAdsTargetProps {
  data?: API.ListingInfo;
}

const InventoryList = [{
  name: ['库存充足', '没有断货风险'],
  value: 'normal'
}, {
  name: ['库存不足', '需降低广告预算以保护库存'],
  value: 'low'
}, {
  name: ['库存严重不足', '即将断货，需暂停广告'],
  value: 'critical'
}];

const ListAdsTarget: React.FC<ListAdsTargetProps> = ({ data: propsData }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [form] = Form.useForm();
  const [hasPromotions, setHasPromotions] = useState(false);
  const [data, setData] = useState<API.ListingInfo | null>(null);
  const { productInfo } = useModel('productInfo');
  const product = useMemo(() => {
    return {
      asin: productInfo?.asin || '',
      title: productInfo?.title || '',
      price: productInfo?.price || 0,
      rating: productInfo?.rating || 0,
      reviewCount: productInfo?.review_count || 0,
      imageUrl: getResizedAmazonImageUrl(productInfo?.image_url || ''),
      country: productInfo?.country || 'US',
      url: productInfo?.url || '',
      storeName: productInfo?.store_name || ''
    }
  }, [productInfo])
  useEffect(() => {
    if (propsData) {
      setData(propsData)
      setHasPromotions(propsData.promotion_plan?.has_promotion || false)
    }
  }, [propsData])

  const showModal = () => {
    if (!data) return;

    // 设置促销状态和库存状态的初始值
    setHasPromotions(data.promotion_plan?.has_promotion || false);

    // 打开弹框时设置表单值，实现数据回显
    const formValues: any = {
      ai_instruction: data.ai_instruction,
      promotion_plan: {
        has_promotion: data.promotion_plan?.has_promotion,
        promotion_list: data.promotion_plan?.promotion_list || []
      } as PromotionPlan,
      inventory_status: InventoryList.find(item => item.value === data.inventory_status)?.value || 'normal',
    };

    form.setFieldsValue(formValues);

    setIsModalOpen(true);
  };

  const handleOk = () => {
    form.validateFields().then(values => {

      if (!data) return;
      // 处理表单数据
      const formData = {
        ai_instruction: values.ai_instruction,
        promotion_plan: values.promotion_plan.has_promotion ? {
          has_promotion: true,
          promotion_list: (values.promotion_plan.promotion_list || []).map((item: PromotionItem) => ({
            id: item.id,
            time_range: item.time_range,
            promotion_type: item.promotion_type,
            discount_price: Number(item.discount_price || 0)
          }))
        } as PromotionPlan : { has_promotion: false } as PromotionPlan,
        inventory_status: values.inventory_status
      };

      updateListing({
        parent_asin: data.parent_asin,
        profile_id: data.profile_id,
        ...formData
      })
        .then((res: any) => {
          if (res.code === 200) {
            setData(res.data)
            message.success('更新成功')
          }
        })
        .catch(() => {
          message.error('更新失败')
        })

      setIsModalOpen(false);
    }).catch(info => {
      console.log('表单验证失败:', info);
    });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  if (!data) return null;

  // 将广告目标文本拆分为描述列表
  const adTargetDescriptions = data.ai_instruction ? data.ai_instruction.split('\n') : [];

  return (
    <Space direction="vertical" style={{ width: '100%' }} size={'middle'}>
      <div className={styles.container}>
        {/* AI指令 */}
        <Card className="card" style={{width:'33%'}}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>AI指令</h3>
            <Button type="text" icon={<EditOutlined />} onClick={showModal} />
          </div>
          <div>
            <ul className="goalList" style={{overflowY: 'auto', maxHeight: '180px'}}>
              {adTargetDescriptions.map((desc: string, index: number) => (
                <li key={index} className="goalItem">
                  { desc && !Object.values(AiOptionList).some(option => option.name === desc.replace('：', '')) && <div className="goalDot"></div> }
                  <Text style={{ textAlign: 'left', lineHeight: '1.5' }}>{desc}</Text>
                </li>
              ))}
            </ul>
          </div>
        </Card>

        {/* 促销计划 */}
        <Card className="card" style={{width:'33%'}}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>促销计划</h3>
            <Button type="text" icon={<EditOutlined />} onClick={showModal} />
          </div>
          <PromotionInfo promotion_plan={data.promotion_plan} />
        </Card>

        {/* 库存状态 */}
        <Card className="card" style={{width:'33%'}}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>库存状态</h3>
            <Button type="text" icon={<EditOutlined />} onClick={showModal} />
          </div>
          <div>
            <Row gutter={[16, 16]}>
              <Col span={16}>
                {InventoryList.find(item => item.value === data.inventory_status)?.name.map((name, index) => (
                  <div key={index}>{name}</div>
                ))}
              </Col>
            </Row>
          </div>
        </Card>
      </div>

      {/* 编辑弹窗 */}
      <Modal
        // title="Listing 相关信息修改"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={1280}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleOk}>
            保存
          </Button>,
        ]}
        destroyOnClose
        centered
      >
        <ListingForm
          form={form}
          hasPromotions={hasPromotions}
          onPromotionChange={setHasPromotions}
          showProductInfo={true}
          product={product}
        />
      </Modal>
    </Space >
  );
};

export default ListAdsTarget;
