import React from 'react';
import { Tabs, Row, Col } from 'antd';
import './style.less';

const { TabPane } = Tabs;

interface PromotionItem {
  time_range: [string, string];
  promotion_type?: string;
  discount_price?: number;
}

interface PromotionInfoProps {
  promotion_plan?: {
    has_promotion: boolean;
    promotion_list?: PromotionItem[];
  };
}

const PromotionInfo: React.FC<PromotionInfoProps> = ({ promotion_plan }) => {
  if (!promotion_plan?.has_promotion || !promotion_plan.promotion_list || promotion_plan.promotion_list.length === 0) {
    return <div>无促销</div>;
  }

  if (promotion_plan.promotion_list.length === 1) {
    const promotion = promotion_plan.promotion_list[0];
    return (
      <div>
        <ul className="goalList">
          <li className="goalItem">
            <div className="goalDot"></div>
            <div style={{ flex: 1 }}>
              <Row gutter={[16, 16]}>
                <Col span={8}>促销日期：</Col>
                <Col span={16}>{promotion.time_range?.[0]} - {promotion.time_range?.[1]}</Col>
              </Row>
            </div>
          </li>
          <li className="goalItem">
            <div className="goalDot"></div>
            <div style={{ flex: 1 }}>
              <Row gutter={[16, 16]}>
                <Col span={8}>促销类型：</Col>
                <Col span={16}>{promotion.promotion_type}</Col>
              </Row>
            </div>
          </li>
          <li className="goalItem">
            <div className="goalDot"></div>
            <div style={{ flex: 1 }}>
              <Row gutter={[16, 16]}>
                <Col span={8}>最终折扣价格：</Col>
                <Col span={16}>${promotion.discount_price}</Col>
              </Row>
            </div>
          </li>
        </ul>
      </div>
    );
  }

  return (
    <Tabs defaultActiveKey="0">
      {promotion_plan.promotion_list.map((promotion: PromotionItem, index: number) => (
        <TabPane tab={`促销${index + 1}`} key={index.toString()}>
          <div>
            <ul className="goalList">
              <li className="goalItem">
                <div className="goalDot"></div>
                <div style={{ flex: 1 }}>
                  <Row gutter={[16, 16]}>
                    <Col span={8}>促销日期：</Col>
                    <Col span={16}>{promotion.time_range?.[0]} - {promotion.time_range?.[1]}</Col>
                  </Row>
                </div>
              </li>
              <li className="goalItem">
                <div className="goalDot"></div>
                <div style={{ flex: 1 }}>
                  <Row gutter={[16, 16]}>
                    <Col span={8}>促销类型：</Col>
                    <Col span={16}>{promotion.promotion_type}</Col>
                  </Row>
                </div>
              </li>
              <li className="goalItem">
                <div className="goalDot"></div>
                <div style={{ flex: 1 }}>
                  <Row gutter={[16, 16]}>
                    <Col span={8}>最终折扣价格：</Col>
                    <Col span={16}>${promotion.discount_price}</Col>
                  </Row>
                </div>
              </li>
            </ul>
          </div>
        </TabPane>
      ))}
    </Tabs>
  );
};

export default PromotionInfo;