import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Button, Modal, Typography, Space } from 'antd';
import { useRequest, useSearchParams, useModel } from '@umijs/max';
import { getMessages } from '@/services/ibidder_api/user';
import WeekMonthAnalysisContent from '../AiWork/components/weekAnalysis';
import WeekStrategyContent from '../AiWork/components/weekStrategy';
import DayStrategyContent from '../AiWork/components/dayStragegy';
import { markRead } from '@/services/ibidder_api/operation'; // 引入 markRead 服务
import dayjs from 'dayjs';
import { getDayOfWeek } from '@/utils/bus';
import EmptyState from '../EmptyState';
const { Title } = Typography;

const job_idMap = {
  'market_report_month': '下月市场分析报告',
  'ads_strategy_week': '下周广告投放策略',
  'ads_strategy_day': '明天广告投放策略',
}

const titleMap = {
  'market_report_month': '月市场分析报告',
  'ads_strategy_week': '周广告投放策略',
  'ads_strategy_day': '日广告投放策略',
}

const ConfirmAdStrategyAlert: React.FC = () => {
  const [searchParams] = useSearchParams();
  const { triggerRefresh } = useModel('strategyRefresh');
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const country = searchParams.get('country') as string;
  const { updateUnreadCount } = useModel('unreadCount');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedData, setSelectedData] = useState<any>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [modelTitle, setModelTitle] = useState<JSX.Element>();
  const [currentMessageId, setCurrentMessageId] = useState<number | null>(null);
  const { updateAlert } = useModel('updateAlert');
  const prevEsIdRef = useRef<string>('');
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const countRef = useRef<number>(0);
  const { data: response, refresh } = useRequest(() =>
    getMessages({
      is_read: 0,
      message_type: 5,
      page_no: 1,
      page_size: 1, // 先获取10条
      parent_asin: asin,
      profile_id: profile_id,
    }),
  );

  // 当response数据变化时，更新全局unreadCount
  useEffect(() => {
    if (response?.total !== undefined) {
      updateUnreadCount(response.total);
    }
  }, [response, updateUnreadCount]);

  // 监测第一条数据的 es_id 变化
  useEffect(() => {
    const messageListData = response?.list;
    if (messageListData && messageListData.length > 0) {
      const firstMessage = messageListData[0];
      const currentEsId = firstMessage.extra_data?.es_id || '';

      // 如果有数据且 es_id 发生变化（不是初始状态）
      if (prevEsIdRef.current !== '' && prevEsIdRef.current !== currentEsId) {
        triggerRefresh();
      }

      // 更新上一次的 es_id
      prevEsIdRef.current = currentEsId;
    }
  }, [response, triggerRefresh]);

  useEffect(() => {
    if (updateAlert) {
      // 如果updateAlert变成true，取消之前的定时器
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      // 重置计数器
      countRef.current = 0;
      // 开始新的定时器，执行10次
      intervalRef.current = setInterval(() => {
        countRef.current++;
        refresh();

        // 如果已经执行了10次，清除定时器
        if (countRef.current >= 10) {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
        }
      }, 30_000);
    }
  }, [refresh, updateAlert]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);

  const markItemRead = async () => {
    if (currentMessageId === null) return;
    await markRead({ message_ids: [currentMessageId] });
    setTimeout(() => {
      triggerRefresh();
    }, 1000);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setSelectedData(null);
    setIsEditMode(false);
    setCurrentMessageId(null); // 清除 messageId
    refresh(); // 关闭弹窗后刷新列表
  };

  const renderModalContent = () => {
    if (!selectedData) return null;
    const reportData = selectedData.report_data;
    if (!reportData) return <EmptyState imageWidth={180} />;

    if (reportData.market_report_month) {
      return <WeekMonthAnalysisContent
        key={selectedData.id}
        asins={selectedData.asins}
        type="month"
        isEdit={isEditMode}
        onCancel={handleCancel}
        onSuccess={markItemRead} // 添加 markRead prop
        asin={asin}

        profile_id={selectedData.profile_id}
        esId={selectedData.es_id}
        role={selectedData.role}
        job_id={selectedData.job_id}
        current_time={selectedData.current_time}
        monthly_trends={reportData.market_trends?.monthly_trends}
        start_date={reportData.market_report_month.start_date}
      />
    }
    if (reportData.ads_strategy_week) {
      return <WeekStrategyContent
        key={selectedData.id}
        asin={asin}

        esId={selectedData.es_id}
        asins={selectedData.asins}
        profile_id={selectedData.profile_id}
        job_id={selectedData.job_id}
        role={selectedData.role}
        current_time={selectedData.current_time}
        isCompleteStrategy={true}
        isEdit={isEditMode}
        onCancel={handleCancel}
        onSuccess={markItemRead} // 添加 markRead prop
        start_date={reportData.ads_strategy_week.start_date}
      />
    }
    if (reportData.ads_strategy_day) {
      return <DayStrategyContent
        key={selectedData.id}
        isEdit={isEditMode}
        onCancel={handleCancel}
        onSuccess={markItemRead} // 添加 markRead prop
        asin={asin}
        asins={selectedData.asins}
        profile_id={selectedData.profile_id}
        esId={selectedData.es_id}
        job_id={selectedData.job_id}
        role={selectedData.role}
        current_time={selectedData.current_time}
        date={reportData.ads_strategy_day.date}
      />
    }
    return <p>无法识别的报告类型。</p>;
  };

  // if (error) {
  //   return <Alert message="加载提醒失败" type="error" showIcon />;
  // }

  const messageListData = response?.list;
  // 如果没有需要确认的消息，则不显示任何内容
  if (!messageListData || messageListData.length === 0) {
    return null;
  }

  // 只显示第一条消息
  const firstMessage = messageListData[0];
  const hasReportData = !!firstMessage.extra_data?.report_data;

  if (!hasReportData) {
    return null;
  }

  const getShowDate = (item: any) => {
    if (item.extra_data.job_id === 'ads_strategy_week') {
      const formattedStartDate = item.extra_data.report_data.ads_strategy_week.start_date
      const formattedEndDate = item.extra_data.report_data.ads_strategy_week.end_date
      return `（${formattedStartDate} ~ ${formattedEndDate}）`;
    }
    if (item.extra_data.job_id === 'market_report_month') {
      const formattedStartDate = item.extra_data.report_data.market_report_month.start_date
      const formattedEndDate = item.extra_data.report_data.market_report_month.end_date
      return `（${formattedStartDate} ~ ${formattedEndDate}）`;
    }
    if (item.extra_data.job_id === 'ads_strategy_day') {
      return `（${item.extra_data.report_data.ads_strategy_day?.date || ''}${getDayOfWeek(item.extra_data.report_data.ads_strategy_day?.date || '')}）`;
    }
  }

  const showModal = (item: any, editMode = true) => {
    setModelTitle(<Title level={2} style={{ margin: 0 }}>
      {titleMap[item.extra_data.job_id as keyof typeof titleMap]}{getShowDate(item)}
    </Title>);
    setSelectedData(item.extra_data);
    setCurrentMessageId(item.id); // 记录 messageId
    setIsEditMode(editMode);
    setIsModalOpen(true);
  };

  if (firstMessage.extra_data.can_edit === false) {
    return null
  }

  return (
    <>
      <Alert
        style={{ marginBottom: 16 }}
        message={
          <Space style={{ flex: 1, justifyContent: 'space-between', width: '100%', paddingRight: '1em' }}>
            <span>
              请您审核
              <span
                style={{ color: '#4C6FFF', cursor: 'pointer' }}
                onClick={() => showModal(firstMessage, false)} // 注意这里传入的是 firstMessage
              >
                {job_idMap[firstMessage.extra_data.job_id as keyof typeof job_idMap]}
              </span>
              {getShowDate(firstMessage)}
            </span>
            <span>
              {dayjs(firstMessage.created_at).format('YYYY-MM-DD HH:mm')}
            </span>
          </Space>
        }
        type="info"
        showIcon
        action={
          <Button
            size="small"
            type="primary"
            onClick={() => showModal(firstMessage)} // 注意这里传入的是 firstMessage
          >
            去审核
          </Button>
        }
      />

      <Modal
        title={modelTitle}
        open={isModalOpen}
        onCancel={handleCancel}
        footer={null}
        width="90%"
        destroyOnClose
        className='report-modal'
      >
        {renderModalContent()}
      </Modal>
    </>
  );
};

export default ConfirmAdStrategyAlert;