.container {
}

.title {
  margin-top: 1em;
  margin-bottom: 24px;
  font-weight: bold;
  font-size: 18px;
}



// AI工作动态相关样式
.aiWorkContainer {
  background-color: #fff;
  border-radius: 8px;
}

.aiWorkTabs {
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
}

.aiWorkList {
  padding: 16px;
}

.aiWorkItem {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.aiWorkContent {
  flex: 1;
  display: flex;
  align-items: flex-start;
  flex-wrap: nowrap;
}

.aiWorkHeader {
  display: flex;
  margin-bottom: 8px;
}

.aiWorkRole {
  height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  margin-right: 16px;
}

.todoRole {
  height: 48px;
  display: inline-flex;
  flex-direction: column;
  justify-content: space-around;
  margin-right: 16px;
}

.aiWorkDesc {
  font-size: 12px;
  color: #8c8c8c;
}

.aiWorkType {
  font-weight: bold;
  color: #333;
  white-space: nowrap;
  width: 100px;
}

.aiWorkBaseOn {
  color: #8c8c8c;
}

.aiWorkMainContent {
  flex: 1;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
}

.aiWorkLink {
  color: #1890ff;
  margin-left: 8px;
  white-space: nowrap;
}

.aiWorkTimestamp {
  color: #8c8c8c;
  font-size: 12px;
  white-space: nowrap;
  margin-left: 2em;
}


.highlight {
  color: #1890ff;
  cursor: pointer;
}

.boldItem {
  font-weight: bold;
}