.dateNavigatorContainer {
  display: flex;
  align-items: center;
  justify-content: space-between; // 确保左右按钮在两端
  width: 100%;
}

.dateButtonsWrapper {
  overflow-x: auto; // 允许水平滚动
  white-space: nowrap; // 防止按钮换行
  flex-grow: 1; // 占据剩余空间
  margin: 0 8px; // 与导航按钮保持间距

  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none; // Chrome, Safari
  }
  -ms-overflow-style: none;  // IE, Edge
  scrollbar-width: none;  // Firefox
}

.dateButton {
  min-width: 120px; // 确保按钮有足够的宽度显示日期和“今天”
  text-align: center;
}

.todayTag {
  font-size: 12px;
}