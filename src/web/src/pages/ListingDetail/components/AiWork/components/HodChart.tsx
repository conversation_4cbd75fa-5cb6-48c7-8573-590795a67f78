import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface HodChartProps {
  hodBidding: {
    hour: number;
    adjustment: number;
    rationale: string;
  }[];
}

const HodChart: React.FC<HodChartProps> = ({ hodBidding }) => {
  const chartRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // 检查 DOM 元素和数据是否存在
    if (!chartRef.current || !hodBidding || hodBidding.length === 0) {
      return;
    }
    
    // 确保在已有图表实例时先销毁
    let chartInstance = echarts.getInstanceByDom(chartRef.current);
    if (chartInstance) {
      chartInstance.dispose();
    }
    
    // 初始化图表
    chartInstance = echarts.init(chartRef.current);
    
    const data = hodBidding.map((item) => [
      `${item.hour}:00`,
      item.adjustment
    ]);
    
    const option = {
      tooltip: {
        trigger: 'axis',
        confine: true,
        formatter: function(params: any) {
          const value = params[0].value;
          const index = params[0].dataIndex;
          return `<div>
          <div>调整比例: ${value > 0 ? '+' : ''}${(value * 100).toFixed(2)}%</div>
          <div>调整理由: ${hodBidding[index].rationale}</div>

          </div>`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: 30,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item[0]),
        boundaryGap: true,
        axisLabel: {
          interval: 0,
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: (value: number) => `${value > 0 ? '+' : ''}${value * 100}%`
        }
      },
      series: [{
        data: data.map(item => item[1]),
        type: 'bar',
        itemStyle: {
          color: function(params: any) {
            return params.value >= 0 ? '#ff4d4f' : '#52c41a';
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: function(params: any) {
            return `${params.value > 0 ? '+' : ''}${(params.value * 100).toFixed(2)}%`;
          }
        },
        markLine: {
          silent: true,
          symbol: ['none', 'none'],
          lineStyle: {
            color: '#ccc',
            type: 'dashed'
          },
          data: [{ yAxis: 0 }]
        }
      }]
    };
    
    chartInstance.setOption(option);
    
    const handleResize = () => {
      chartInstance && chartInstance.resize();
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      chartInstance && chartInstance.dispose();
      window.removeEventListener('resize', handleResize);
    };
  }, [hodBidding]);

  return <div ref={chartRef} style={{ width: '100%', height: '400px' }} />;
};

export default HodChart;