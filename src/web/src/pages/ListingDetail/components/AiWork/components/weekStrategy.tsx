import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Typography, Statistic, Table, Tag, Space, Flex, message, Select, Spin } from 'antd';
import { useModel, useSearchParams } from '@umijs/max';
import { approveReportWithMessageId, generateVersionOptions, shouldShowConfirmationSection } from '@/utils/bus';
import styles from './index.less';
import dayjs from 'dayjs';
import ExpectedResults from '../../ExpectedResults';

import { getBaseReport, getAdStrategy, editReport, getRoleAgentDetail } from '@/services/ibidder_api/operation';
import WeekAnalysisCard from '../../WeekAnalysisCard';
import RevisionHistory, { Revision_history } from './RevisionHistory';
import ConfirmationSection from './ConfirmationSection';
import FeedbackButton from './FeedbackButton';
import DateNavigator from './DateNavigator';
import EmptyState from '../../EmptyState';

const { Title, Text } = Typography;

export interface Real_ads_result {
  acos: number | null;
  clicks: number | null;
  cvr: number | null;
  impressions: number | null;
  orders: number | null;
  sales: number | null;
  spend: number | null;
}

export interface Weekly_strategy {
  approach: string;
  guideline: string;
  budget_range: { min: number; max: number };
  bid_adjustment_range: { min: number; max: number };
  week_start_date: string; // 不再是可选属性，确保与 weekAnalysis.tsx 中的定义兼容
  strategy: string; // 不再是可选属性，确保与 weekAnalysis.tsx 中的定义兼容
}

export interface WeekStrategyData {
  start_date: string;
  end_date: string;
  current_time: string;
  approach: string;
  rationale: string;
  revision_history?: Revision_history;
  ads_suggestion: {
    approach: string;
    primary_goal: {
      goal: string;
      rationale: string;
    };
    other_goals: string[];
    rationale: string;
  };
  primary_goal: {
    goal: string;
    rationale: string;
  };
  other_goals: string[];
  week_budget: {
    typical: number;
    min: number;
    max: number;
    last_week_budget: number;
    change_from_last_week: string;
    rationale: string;
  };
  bid_adjustment_range: {
    min: number;
    max: number;
    rational: string;
  };
  daily_strategy_suggestion: {
    [date: string]: {
      approach: string;
      guideline: string;
      budget_range: { min: number; max: number };
      bid_adjustment_range: { min: number; max: number };
    };
  };
  weekly_strategy: Weekly_strategy[];
  week_expected_result: {
    spend: {
      typical: number;
      min: number;
      max: number;
      last_week: number;
      change_from_last_week: string;
    };
    sales: {
      typical: number;
      min: number;
      max: number;
      last_week: number;
      change_from_last_week: string;
    };
    orders: {
      typical: number;
      min: number;
      max: number;
      last_week: number;
      change_from_last_week: string;
    };
    cvr: {
      typical: number;
      min: number;
      max: number;
      last_week: number;
      change_from_last_week: string;
    };
    acos: {
      typical: number;
      min: number;
      max: number;
      last_week: number;
      change_from_last_week: string;
    };
  };
  tips_for_week_after_target_week: string[];
}

interface DayStrategyData {
  date: string;
  approach: string;
  guideline: string;
  budget_range: { min: number; max: number };
  bid_adjustment_range: { min: number; max: number };
}

interface WeekStrategyContentProps {
  asin: string;
  asins?: string[];
  parent_asin?: string;
  profile_id: string;
  date?: string;
  /** 是否是从"查看本周完整策略"按钮进入 */
  isCompleteStrategy?: boolean;
  job_id: string;
  esId?: string;
  target_job_id?: string;
  current_time: string;
  isEdit?: boolean;
  onCancel?: (refresh: boolean) => void;
  onSuccess: () => void;
  country?: string;
  role?: string;
  real_ads_result?: Real_ads_result;
  showHistoryVersion?: boolean;
  showDateNavigator?: boolean;
  start_date?: string;
  end_date?: string;
  controlBtn?: React.ReactNode;
  /** 审核区域是否显示 看板上不显示 */
  showReviewArea?: boolean;
}

const WeekStrategyContent: React.FC<WeekStrategyContentProps> = (props) => {
  const {
    asin,
    asins,
    date,
    job_id,
    target_job_id,
    isCompleteStrategy = false,
    onCancel = () => { },
    onSuccess,
    real_ads_result,
    country,
    role,
    showHistoryVersion = true,
    showDateNavigator = true,
    start_date,
    controlBtn,
    showReviewArea = true
  } = props

  // 全局状态管理
  const { updateAlertFn } = useModel('updateAlert');
  const [searchParams] = useSearchParams();
  const parent_asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const [weekStrategyData, setWeekStrategyData] = useState<WeekStrategyData | null>(null);
  const [_real_ads_result, setReal_ads_result] = useState<Real_ads_result | undefined>(real_ads_result);
  const [es_id, setEs_id] = useState<string>('');
  const [latestEsId, setLatestEsId] = useState<string>(''); // 专门存储当前日期最新的esid
  const [isEdit, setIsEdit] = useState(props.isEdit ?? false);
  const [current_time, setCurrent_time] = useState(props.current_time);
  const [aiFeedbackContent, setAiFeedbackContent] = useState<string>('');
  // 保存 start_date 参数，用于 DateNavigator 组件显示默认值
  const [currentStartDate, setCurrentStartDate] = useState<string>(start_date || '');
  const [loading, setLoading] = useState(false);

  // 处理周导航器数据更新的回调函数
  const handleStrategyDataUpdate = (data: any | null) => {
    if (data && data.result && data.result.ads_strategy_week) {
      setWeekStrategyData(data.result.ads_strategy_week);
      setReal_ads_result(data.result.real_ads_result);
      setEs_id(data.es_id);
      setLatestEsId(data.es_id); // 保存最新的esid
      setIsEdit(data.can_edit || false);
      processVersionInfo(data.es_id);
      setCurrent_time(data.current_time); // 设置当前时间

      // 更新 currentStartDate，用于 DateNavigator 显示
      if (data.result.ads_strategy_week.start_date) {
        setCurrentStartDate(data.result.ads_strategy_week.start_date);
      }
    } else {
      setWeekStrategyData(null);
    }
  };



  // 版本管理状态
  const [currentVersion, setCurrentVersion] = useState<number>(1);
  const [maxVersion, setMaxVersion] = useState<number>(1);
  const [versionOptions, setVersionOptions] = useState<{ label: string, value: number }[]>([]);

  // 处理版本信息的独立方法 - 基于最新的esid处理版本
  const processVersionInfo = (esId: string) => {
    if (!esId) return;

    const parts = esId.split('_');
    if (parts.length > 1) {
      const version = parseInt(parts[1]);
      setMaxVersion(version); // 设置最大版本号
      setVersionOptions(generateVersionOptions(version)); // 版本选项只基于最新esid
      setCurrentVersion(version); // 设置当前版本为最新版本
    }
  };


  // 获取周策略数据
  const fetchWeekStrategyData = async () => {
    try {
      setLoading(true);

      // 设置初始状态
      setReal_ads_result(real_ads_result);
      setEs_id(props.esId || '');
      setLatestEsId(props.esId || '');
      setIsEdit(props.isEdit || false);
      setCurrent_time(props.current_time);

      if (isCompleteStrategy) {
        // 从"查看本周完整策略"按钮进入，使用 getWeekStrategy 接口
        const res: any = await getAdStrategy({
          date: date || dayjs(new Date()).format('YYYY-MM-DD'),
          asin,
          profile_id,
        });
        setLoading(false);

        if (res.code === 200 && res.data && res.data.result.ads_strategy_week) {
          setWeekStrategyData(res.data.result.ads_strategy_week);
          setReal_ads_result(res.data.result.real_ads_result);
          setEs_id(res.data.es_id);
          setLatestEsId(res.data.es_id); // 保存最新的esid
          setCurrent_time(res.data.current_time); // 设置当前时间

          // 更新 currentStartDate，用于 DateNavigator 显示
          if (res.data.result.ads_strategy_week.start_date) {
            setCurrentStartDate(res.data.result.ads_strategy_week.start_date);
          }

          // 处理版本信息 - 只在这里设置版本
          processVersionInfo(res.data.es_id);
          return;
        }
      } else if (asin && profile_id && job_id && props.current_time) {
        // 其他情况使用 getBaseReport 接口
        const res: any = await getBaseReport({
          job_id,
          asin,
          profile_id: profile_id.toString(),
          current_time: props.current_time,
          target_job_id
        });
        setLoading(false);
        if (res.data && res.data.result && res.data.result.ads_strategy_week) {
          setWeekStrategyData(res.data.result.ads_strategy_week);
          setReal_ads_result(res.data.result.real_ads_result);
          setEs_id(res.data.es_id || '');
          setLatestEsId(res.data.es_id || ''); // 保存最新的esid
          setIsEdit(res.data.can_edit || false);
          setCurrent_time(res.data.current_time); // 设置当前时间

          // 更新 currentStartDate，用于 DateNavigator 显示
          if (res.data.result.ads_strategy_week.start_date) {
            setCurrentStartDate(res.data.result.ads_strategy_week.start_date);
          }

          // 处理版本信息 - 只在这里设置版本
          processVersionInfo(res.data.es_id || '');
          return;
        }
      }
    } catch (error) {
      console.error('获取周策略数据失败:', error);
      message.error('获取数据失败，请重试');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWeekStrategyData();
  }, [asin, profile_id, date, isCompleteStrategy, job_id, props.current_time, props.esId]);

  // 转换每日预算数据为表格所需格式
  const getDailyStrategyData = () => {
    if (!weekStrategyData) return []
    const dailyData: DayStrategyData[] = [];

    Object.keys(weekStrategyData.daily_strategy_suggestion).forEach((date) => {
      const data = weekStrategyData.daily_strategy_suggestion[date];
      dailyData.push({
        date,
        ...data
      });
    });

    // 按日期升序排列
    dailyData.sort((a, b) => a.date.localeCompare(b.date));

    return dailyData;
  };

  const dailyStrategyData = getDailyStrategyData();

  // 版本切换处理函数 - 基于最新esid构建目标esid
  const handleVersionChange = async (version: number) => {
    setCurrentVersion(version);
    if (!latestEsId) return;

    const parts = latestEsId.split('_');
    if (parts.length > 1) {
      const targetEsId = `${parts[0]}_${version}`;
      setEs_id(targetEsId);
      setLoading(true);
      try {
        const res: any = await getRoleAgentDetail({ es_id: targetEsId });
        setLoading(false);

        if (res && res.data && res.data.result && res.data.result.ads_strategy_week) {
          const responseData = res.data;
          if (responseData.result && responseData.result.ads_strategy_week) {
            setWeekStrategyData(responseData.result.ads_strategy_week);
            setReal_ads_result(responseData.result.real_ads_result);
            setIsEdit(responseData.can_edit);
            setCurrent_time(responseData.current_time); // 设置当前时间

            // 更新 currentStartDate，用于 DateNavigator 显示
            if (responseData.result.ads_strategy_week.start_date) {
              setCurrentStartDate(responseData.result.ads_strategy_week.start_date);
            }

            // 版本选项保持不变，因为它们只基于最新esid
            // setVersionOptions 不需要在这里调用
          }
        } else {
          message.error('获取版本数据失败');
        }
      } catch (error) {
        setLoading(false);
        console.error('获取版本数据失败:', error);
        message.error('获取版本数据失败，请重试');
      }
    }
  };

  const handleConfirmChanges = async () => {
    if (!current_time || !asin || !job_id || !profile_id) {
      message.error("缺少必要的参数，无法提交");
      return;
    }

    try {
      await approveReportWithMessageId({
        parent_asin: parent_asin || asin || '',
        profile_id: profile_id,
        country: country || '',
        es_id: es_id || props.esId || '',
        job_id: job_id,
        asins: asins || [asin],
      })

      // 更新全局状态
      updateAlertFn(true);
      onSuccess();

      // 如果没有AI反馈内容，直接关闭弹框
      if (!aiFeedbackContent.trim()) {
        if (onCancel) onCancel(false);
        return;
      }

      // 创建一个深拷贝的数据对象
      const updatedWeekStrategyData = weekStrategyData ? JSON.parse(JSON.stringify(weekStrategyData)) : {};

      // 如果有AI反馈内容，保存到数据对象中
      if (aiFeedbackContent.trim()) {
        updatedWeekStrategyData.ai_feedbackContent = aiFeedbackContent.trim();
      }

      const payload = {
        es_id: es_id || props.esId || '',
        current_time: current_time,
        parent_asin: parent_asin || asin || '',
        asins: asins || [asin],
        role: role || '',
        job_id: job_id,
        profile_id: profile_id,
        country: country || '',
        data: {
          ads_strategy_week: updatedWeekStrategyData,
        },
      };
      const res: any = await editReport(payload);
      if (res && res.code === 200) {
        message.success('修改已成功提交');
        if (onCancel) onCancel(true); // 只有成功时才关闭弹框
      } else {
        message.error(res?.message || '修改提交失败');
      }
    } catch (error: any) {
      message.error('提交修改失败，请重试');
    }
  }

  // 每日预算表格列配置
  const dayBudgetColumns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: '15%',
      render: (text: string) => {
        // 使用dayjs解析日期并获取星期几
        const weekdayMap = ['日', '一', '二', '三', '四', '五', '六'];
        const weekday = weekdayMap[dayjs(text).day()];
        return `${text}（周${weekday}）`;
      }
    },
    {
      title: '策略方法',
      dataIndex: 'approach',
      key: 'approach',
      width: '10%',
      render: (text: string) => {
        let color = 'blue';
        if (text === 'aggressive') {
          color = 'red';
        } else if (text === 'conservative') {
          color = 'green';
        }
        return (
          <Tag color={color}>
            {text === 'balanced' ? '平衡' :
              text === 'aggressive' ? '激进' :
                text === 'conservative' ? '保守' : text}
          </Tag>
        );
      }
    },
    {
      title: '预算范围',
      key: 'budget',
      width: '15%',
      render: (_: any, record: DayStrategyData) => (
        <Text strong>${record.budget_range.min} ~ ${record.budget_range.max}</Text>
      )
    },
    {
      title: '竞价调整范围',
      key: 'bid_adjustment',
      width: '18%',
      render: (_: any, record: DayStrategyData) => {
        const minValue = record.bid_adjustment_range.min * 100;
        const maxValue = record.bid_adjustment_range.max * 100;
        let minColor = 'blue';
        if (minValue > 0) {
          minColor = 'red';
        } else if (minValue < 0) {
          minColor = 'green'
        }
        let maxColor = 'blue';
        if (maxValue > 0) {
          maxColor = 'red';
        } else if (maxValue < 0) {
          maxColor = 'green'
        }
        return (
          <Space>
            <Tag color={minColor}>{minValue}%</Tag>
            <Text>至</Text>
            <Tag color={maxColor}>{maxValue}%</Tag>
          </Space>
        );
      }
    },
    {
      title: '调整理由',
      dataIndex: 'guideline',
      key: 'guideline',
      // width: '45%',
      render: (text: string) => (
        <Text style={{ fontSize: '14px' }}>{text}</Text>
      )
    }
  ];

  const contentCards = !weekStrategyData ?
    <EmptyState
      imageWidth={180}
      text="未查到对应日期的报告"
    />
    :
    <>
      <Title level={3} className={styles.title}>投放策略</Title>
      <WeekAnalysisCard
        ads_suggestion={weekStrategyData}
      />

      <Title level={3} className={styles.title} style={{ marginTop: "48px" }}>本周预期结果</Title>
      <ExpectedResults
        spend={weekStrategyData.week_expected_result.spend}
        sales={weekStrategyData.week_expected_result.sales}
        acos={weekStrategyData.week_expected_result.acos}
        cvr={weekStrategyData.week_expected_result.cvr}
        real_ads_result={_real_ads_result}
      />

      {/* 预算和竞价调整板块 */}
      <Title level={3} className={styles.title} style={{ marginTop: "48px" }}>预算和竞价调整</Title>
      <Card className="card" style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <Flex justify='space-between' style={{ marginBottom: 16 }}>
          <Title level={5} style={{ fontSize: '16px' }}>本周预算</Title>
        </Flex>
        <Row gutter={[24, 24]} style={{ flex: 1 }}>
          <Col span={10}>
            <Statistic
              title={<div style={{ fontSize: '14px', color: '#8c8c8c', marginBottom: 8 }}>建议预算</div>}
              value={weekStrategyData.week_budget.typical}
              prefix="$"
              precision={2}
              valueStyle={{ color: '#333', fontSize: '24px', fontWeight: 'bold' }}
            />
            <Text
              type={parseFloat(weekStrategyData.week_budget.change_from_last_week) > 0 ? "danger" : "success"}
              style={{ fontSize: '14px', display: 'block', marginTop: 4 }}>
              较上周：{weekStrategyData.week_budget.change_from_last_week}
            </Text>
          </Col>
          <Col span={14}>
            <Statistic
              title={<div style={{ fontSize: '14px', color: '#8c8c8c', marginBottom: 8 }}>动态调整范围</div>}
              value={`$${weekStrategyData.week_budget.min} ~ $${weekStrategyData.week_budget.max}`}
              valueStyle={{
                fontSize: '24px',
                fontWeight: 'bold'
              }}
            />
            <Text style={{ fontSize: '14px', color: '#8c8c8c', display: 'block', marginTop: 4 }}>
              AI 会根据本周广告实际表现动态调整
            </Text>
          </Col>
        </Row>
        <div style={{ marginTop: 'auto', paddingTop: 16 }}>
          <Text style={{ fontSize: '14px', color: '#666', display: 'block' }}>
            {weekStrategyData.week_budget.rationale}
          </Text>
        </div>
      </Card>
      <Card className="card" style={{ height: '100%', display: 'flex', flexDirection: 'column', marginTop: "16px" }}>
        <Flex justify='space-between' style={{ marginBottom: 16 }}>
          <Title level={5} style={{ fontSize: '16px' }}>竞价调整</Title>
        </Flex>
        <Row gutter={[24, 24]} style={{ flex: 1 }}>
          <Col span={10}>
            <Statistic
              title={<div style={{ fontSize: '14px', color: '#8c8c8c', marginBottom: 8 }}>建议调整</div>}
              value={weekStrategyData.bid_adjustment_range.min * 100}
              suffix="%"
              precision={0}
              valueStyle={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: '#333'
              }}
            />
          </Col>
          <Col span={14}>
            <Statistic
              title={<div style={{ fontSize: '14px', color: '#8c8c8c', marginBottom: 8 }}>调整范围</div>}
              value={`${(weekStrategyData.bid_adjustment_range.min * 100).toFixed(0)}% ~ ${(weekStrategyData.bid_adjustment_range.max * 100).toFixed(0)}%`}
              valueStyle={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: '#333'
              }}
            />
            <Text style={{ fontSize: '14px', color: '#8c8c8c', display: 'block', marginTop: 4 }}>
              AI 会根据实际情况在调整范围内动态调整
            </Text>
          </Col>
        </Row>
        <div style={{ marginTop: 'auto', paddingTop: 16 }}>
          <Text style={{ fontSize: '14px', color: '#666', display: 'block' }}>
            {weekStrategyData.bid_adjustment_range.rational}
          </Text>
        </div>
      </Card>
      {/* 每日预算与竞价调整板块 */}
      <Flex justify='space-between' align='center' style={{ marginTop: "48px" }}>
        <Title level={3} className={styles.title} style={{ margin: 0 }}>每日投放策略</Title>
      </Flex>
      <Card className="card" style={{ marginTop: "16px" }}>
        <Table
          rowKey={'date'}
          columns={dayBudgetColumns}
          dataSource={dailyStrategyData}
          pagination={false}
        />

      </Card>

      {/* 修订记录 */}
      <RevisionHistory
        revision_history={weekStrategyData?.revision_history}
      />
    </>

  return (
    <div className='report-modal-div'>
      <div className='report-modal-content'>
        {/* 日期导航器和历史版本选择 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '24px', marginBottom: '24px', gap: 16 }}>
          <div style={{ flex: 1, overflow: 'hidden' }}>
            {
              showDateNavigator &&
              <DateNavigator
                initialDate={currentStartDate}
                onStrategyDataUpdate={handleStrategyDataUpdate} // Pass the new callback
                asin={asin || parent_asin}
                profile_id={profile_id}
                job_id={'ads_strategy_week'}
                dateType='range'
                setLoading={setLoading}
              />
            }
          </div>
          {
            (showHistoryVersion && maxVersion > 0 || controlBtn) &&
            <div>
              <Space size={16}>
                {showHistoryVersion && maxVersion > 0 && (
                  <div>
                    <span style={{ fontSize: '14px', color: '#666' }}>修订历史： </span>
                    <Select
                      value={currentVersion}
                      onChange={handleVersionChange}
                      options={versionOptions}
                      style={{ width: 90 }}
                    />
                  </div>
                )}
                {isEdit && controlBtn}
              </Space>
            </div>
          }
          <FeedbackButton
            feedbackParams={{
              parent_asin: parent_asin || asin || '',
              profile_id: profile_id || '',
              job_id: job_id || '',
              es_id: es_id || '',
              current_time: current_time || '',
            }}
          />
        </div>

        {
          loading ?
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <Spin size="large"></Spin>
              <div style={{ marginTop: 10 }}>加载中...</div>
            </div>
            :
            contentCards
        }
      </div>

      {showReviewArea && isEdit && shouldShowConfirmationSection(country || '', 'week') && (
        <ConfirmationSection
          value={aiFeedbackContent}
          onChange={(e) => setAiFeedbackContent(e.target.value)}
          onSubmit={handleConfirmChanges}
          minRows={5}
          maxRows={8}
          placeholder='请输入修改意见，每行一条。AI将根据您的意见进行修订。
例如：
● 将策略修改为平衡
● 本周主要目标是在排名稳定基础上，控制广告支出成本
● 本周销售目标是8000美元，基于这个目标重新制定投放策略
● 将周五、周六的投放策略更改为激进'
        />
      )}
    </div>
  );
};

export default WeekStrategyContent;
