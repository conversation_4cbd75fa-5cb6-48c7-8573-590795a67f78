.mainStrategy {
  display: flex;
  flex-direction: column;

  .strategyContent {
    .strategyValue {
      font-size: 30px;
      font-weight: 600;
      color: #52c41a; // 落后 - 红色
      margin-top: 0;
    }

    .strategyValue.onTrack {
      color: #1890ff; // 正常 - 绿色
    }

    .strategyValue.ahead {
      color: #ff4d4f; // 超前 - 蓝色
    }
  }

  .suggestionTitle {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
  }
}

.metricCard {
  .ant-card-body {
    display: flex;
    flex-direction: column;
  }

  .metricHeader {
    margin-bottom: 12px;

    .metricTitle {
      font-size: 14px;
    }
  }

  .metricValue {
    font-size: 26px;
    font-weight: 600;
    margin-bottom: 16px;
  }

  .metricDetails {
    font-size: 14px;
    display: flex;
    gap: 6px;
    flex-direction: column;

    .detailItem {
      display: flex;
      justify-content: space-between;

      .detailLabel {
        color: #8c8c8c;
      }
    }
  }
}