import React from 'react';
import { Card, Col, Row, Typography, Progress, Tooltip } from 'antd';
import styles from './index.less';
import { processNumberOrString } from '@/utils/bus';

const { Title, Text } = Typography;

const DailyProgressAnalysis: React.FC<{ data?: API.DayProgressAnalysis, overallRationale?: string }> = ({ data, overallRationale }) => {
  if (!data) {
    return null;
  }

  const { budget, sales, acos, cvr, day_performance_vs_target, key_observations } = data;

  const getStatusText = (status: string) => {
    switch (status) {
      case 'on_track':
        return '正常';
      case 'behind':
        return '落后';
      case 'ahead':
        return '超前';
      default:
        return status;
    }
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'on_track':
        return styles.onTrack;
      case 'ahead':
        return styles.ahead;
      case 'behind':
        return styles.behind;
      default:
        return ''; // behind 使用默认红色
    }
  };

  const parsePercentage = (percentStr: string): number => {
    const match = percentStr.match(/(\d+(?:\.\d+)?)/);
    return match ? parseFloat(match[1]) : 0;
  };

  const getProgressColor = (percent: number): string => {
    return percent < 100 ? '#1890ff' : '#ff4d4f';
  };

  const getDiffColor = (diffStr: string): string => {
    // 解析差距值，支持正负数和百分比
    const match = diffStr.match(/([+-]?\d+(?:\.\d+)?)/);
    if (match) {
      const value = parseFloat(match[1]);
      if (value > 0) {
        return '#ff4d4f'; // 红色：比目标值大（不好）
      } else if (value < 0) {
        return '#52c41a'; // 绿色：比目标值小（好）
      }
    }
    return 'inherit'; // 默认黑色
  };

  return (
    <div>
      <Title level={3} style={{ marginBottom: '1em' }}>当日进度评估</Title>
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card className="card">
            <div className={styles.mainStrategy}>
              <div className={styles.strategyContent}>
                <Title level={5}>总体进度</Title>
                <Title level={3} className={`${styles.strategyValue} ${getStatusClass(day_performance_vs_target)}`}>
                  {getStatusText(day_performance_vs_target)}
                </Title>
                <ul className="goalList" style={{overflowY: 'auto', maxHeight: '180px'}}>
                  {key_observations.map((desc: string, index: number) => (
                    <li key={index} className="goalItem">
                      { desc && <div className="goalDot"></div> }
                      <Text style={{ textAlign: 'left', lineHeight: '1.5' }}>{desc}</Text>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card className="card">
            <div className={styles.mainStrategy}>
              <div className={styles.strategyContent}>
                <Title level={5} style={{ marginBottom: '12px' }}>调整思路</Title>
                <Text style={{ lineHeight: '1.8', color: '#0C1D23' }}>{overallRationale}</Text>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
      

      <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
        <Col span={6}>
          <Card className={`${styles.metricCard} card`}>
            <div className={styles.metricHeader}>
              <Title level={5} className={styles.metricTitle}>当前花费</Title>
            </div>
            <div className={styles.metricValue}>{`$${budget.day_budget_spend}`}</div>
            <div className={styles.metricDetails}>
              <div className={styles.detailItem}>
                <Tooltip title={budget.current_spend_progress}>
                  <Progress
                    percent={parsePercentage(budget.current_spend_progress)}
                    size="small"
                    showInfo={false}
                    format={() => budget.current_spend_progress}
                    strokeColor={getProgressColor(parsePercentage(budget.current_spend_progress))}
                  />
                </Tooltip>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">日预算</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{`$${budget.day_budget}`}</Text>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">剩余</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{budget.day_budget_left !== null ? `$${budget.day_budget_left}` : '-'}</Text>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={`${styles.metricCard} card`}>
            <div className={styles.metricHeader}>
              <Title level={5} className={styles.metricTitle}>当前销售</Title>
            </div>
            <div className={styles.metricValue}>{`$${sales.sales_current}`}</div>
            <div className={styles.metricDetails}>
              <div className={styles.detailItem}>
                <Tooltip title={sales.current_progress}>
                  <Progress
                    percent={parsePercentage(sales.current_progress)}
                    size="small"
                    showInfo={false}
                    format={() => sales.current_progress}
                    strokeColor={getProgressColor(parsePercentage(sales.current_progress))}
                  />
                </Tooltip>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">目标销售</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{`$${sales.sales_target}`}</Text>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={`${styles.metricCard} card`}>
            <div className={styles.metricHeader}>
              <Title level={5} className={styles.metricTitle}>当前ACOS</Title>
            </div>
            <div className={styles.metricValue}>{processNumberOrString(acos.current, '%')}</div>
            <div className={styles.metricDetails}>
              <div className={styles.detailItem}>
                <Text type="secondary">目标ACoS</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{processNumberOrString(acos.target, '%')}</Text>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">差距</Text>
                <Text style={{ fontWeight: '500', fontSize: 16, color: getDiffColor(acos.diff) }}>{processNumberOrString(acos.diff, '%')}</Text>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className={`${styles.metricCard} card`}>
            <div className={styles.metricHeader}>
              <Title level={5} className={styles.metricTitle}>当前CVR</Title>
            </div>
            <div className={styles.metricValue}>{processNumberOrString(cvr.current, '%')}</div>
            <div className={styles.metricDetails}>
              <div className={styles.detailItem}>
                <Text type="secondary">目标CVR</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{processNumberOrString(cvr.target, '%')}</Text>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">差距</Text>
                <Text style={{ fontWeight: '500', fontSize: 16, color: getDiffColor(cvr.diff) }}>{processNumberOrString(cvr.diff, '%')}</Text>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DailyProgressAnalysis; 