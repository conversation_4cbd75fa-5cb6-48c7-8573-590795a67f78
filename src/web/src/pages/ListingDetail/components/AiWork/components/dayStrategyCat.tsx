import React, { useState, useEffect } from 'react';
import { Card, Table, Typography, Flex, message, Empty, Button, Row, Col } from 'antd';
import { ArrowRightOutlined } from '@ant-design/icons';
import { getBaseReport, getDaypartReports } from '@/services/ibidder_api/operation';
import { getBudgetColor } from '@/utils/bus';
import DailyProgressAnalysis from './DailyProgressAnalysis';
import { processNumberOrString } from '@/utils/bus';
import { useSearchParams } from '@umijs/max';

const { Title, Text, Paragraph } = Typography;

// 组件的 Props
interface DayStrategyCatContentProps {
  asin?: string;
  profile_id?: string;
  job_id?: string;
  current_time?: string;
  target_job_id?: string;
}

const DayStrategyCatContent: React.FC<DayStrategyCatContentProps> = ({
  asin,
  profile_id,
  job_id,
  current_time,
  target_job_id
}) => {
  const [searchParams] = useSearchParams();
  const country = searchParams.get('country') as string;
  const [strategyData, setStrategyData] = useState<{ daily_adjustment_proposal?: API.DailyAdjustmentProposal } | undefined>();
  const [daypartReports, setDaypartReports] = useState<API.DaypartReportItem[]>([]);
  const [selectedTimeIndex, setSelectedTimeIndex] = useState<number>(0);
  const [currentDisplayData, setCurrentDisplayData] = useState<{ daily_adjustment_proposal?: API.DailyAdjustmentProposal } | undefined>();

  // 获取广告优化调整数据
  const fetchStrategyData = async () => {
    if (asin && profile_id && job_id && current_time) {
      try {
        const res: any = await getBaseReport({
          job_id,
          asin,
          profile_id,
          current_time,
          target_job_id
        });
        if (res.data && res.data.result && (res.data.result.daypart_strategy || res.data.result.ads_strategy_daypart)) {
          setStrategyData(res.data.result.daypart_strategy || res.data.result.ads_strategy_daypart);
        }
      } catch (error) {
        console.error('获取广告优化调整数据失败:', error);
        message.error('获取数据失败，请重试');
      }
    }
  };
  const fetchDaypartReports = async () => {
    const res = await getDaypartReports({
      asin,
      profile_id: profile_id ? parseInt(profile_id) : undefined,
      current_time
    })
    if (res.code === 200) {
      setDaypartReports(res.data || [])
    }
  }

  // 处理按钮点击事件
  const handleTimeButtonClick = (index: number) => {
    setSelectedTimeIndex(index);
    const selectedItem = daypartReports[index];

    // 检查 result.ads_strategy_daypart 是否为 undefined、null 或空对象
    const adsStrategyDaypart = selectedItem?.result?.ads_strategy_daypart;

    if (!adsStrategyDaypart ||
      adsStrategyDaypart === null ||
      (typeof adsStrategyDaypart === 'object' && Object.keys(adsStrategyDaypart).length === 0)) {
      // 如果是 undefined、null 或空对象，设置为 undefined 以显示"暂无数据"
      setCurrentDisplayData(undefined);
    } else {
      // 有有效数据时，设置显示数据
      setCurrentDisplayData(adsStrategyDaypart as { daily_adjustment_proposal?: API.DailyAdjustmentProposal });
    }
  };

  // 格式化时间显示
  const formatTimeDisplay = (currentTime: string, country?: string) => {
    // 提取时间部分 (HH:MM)
    const timePart = currentTime.split(' ')[1] || '';
    // 获取站点代码，默认为传入的country或'US'
    const siteCode = country || 'US';
    return `${timePart} ${siteCode}`;
  };

  useEffect(() => {
    fetchStrategyData();
    fetchDaypartReports();
  }, [asin, profile_id, job_id, current_time]);

  // 当 daypartReports 数据加载完成后，根据 current_time 匹配对应的时间按钮
  useEffect(() => {
    if (daypartReports.length > 0 && current_time) {
      // 从 current_time 中提取时间部分 (HH:MM)
      const currentTimePart = current_time.split(' ')[1] || '';

      // 查找匹配的时间按钮索引
      const matchingIndex = daypartReports.findIndex(item => {
        const itemTimePart = item.current_time.split(' ')[1] || '';
        return itemTimePart === currentTimePart;
      });

      // 如果找到匹配的时间，选择该按钮；否则默认选择第一个
      const selectedIndex = matchingIndex >= 0 ? matchingIndex : 0;
      handleTimeButtonClick(selectedIndex);
    } else if (daypartReports.length > 0) {
      // 如果没有 current_time，默认选择第一个
      handleTimeButtonClick(0);
    }
  }, [daypartReports, current_time]);

  // 当 strategyData 加载完成后，设置为默认显示数据
  useEffect(() => {
    if (strategyData && !currentDisplayData) {
      setCurrentDisplayData(strategyData);
    }
  }, [strategyData]);

  // 优先显示按钮选择的数据，如果有按钮被选中且数据为空，则不回退到默认数据
  const displayData = daypartReports.length > 1 ? currentDisplayData : (currentDisplayData || strategyData);
  const proposal = displayData?.daily_adjustment_proposal;

  const formatBidPercentage = (value: number | string): string => {
    if (typeof value === 'string') {
      value = parseFloat(value);
    }
    const percentage = value * 100;
    // 根据数值是否为0来决定是否显示正负号，0%不显示符号
    if (value === 0 && percentage === 0) { // Ensure 0 also gets '0%'
      return `0%`;
    }
    return `${value > 0 ? '+' : ''}${percentage.toFixed(0)}%`;
  };

  const campaignColumns = [
    {
      title: '广告活动',
      dataIndex: 'campaign_name',
      key: 'campaign_name',
      width: 200,
      fixed: 'left' as const,
      render: (text: string) => <Text>{text}</Text>,
    },
    {
      title: '花费',
      dataIndex: 'current_spend',
      key: 'current_spend',
      width: 100,
      render: (current_spend: number | string) => {
        if (current_spend === undefined || current_spend === null) return <Text>-</Text>;
        return <Text>${current_spend}</Text>;
      },
    },
    {
      title: '销售额',
      dataIndex: 'current_sales',
      key: 'current_sales',
      width: 100,
      render: (current_sales: number | string) => {
        if (current_sales === undefined || current_sales === null) return <Text>-</Text>;
        return <Text>${current_sales}</Text>;
      },
    },
    {
      title: 'ACoS',
      dataIndex: 'current_acos',
      key: 'current_acos',
      width: 100,
      render: (current_acos: string) => {
        if (current_acos === undefined || current_acos === null) return <Text>-</Text>;
        return <Text>{processNumberOrString(current_acos)}%</Text>;
      },
    },
    {
      title: 'CVR',
      dataIndex: 'current_cvr',
      key: 'current_cvr',
      width: 100,
      render: (current_cvr: string) => {
        if (current_cvr === undefined || current_cvr === null) return <Text>-</Text>;
        return <Text>{processNumberOrString(current_cvr)}%</Text>;
      },
    },
    {
      title: '预算调整',
      key: 'budget',
      width: 140,
      render: (_: any, record: API.CampaignAdjustmentItem) => {
        if (!record.budget_old || !record.budget_new) return <Text>-</Text>;
        const budget_old = Number(record.budget_old);
        const budget_new = Number(record.budget_new);
        return (
          <Text>
            ${budget_old.toFixed(2)} → <span style={{ color: getBudgetColor(budget_old, budget_new) }}>${budget_new.toFixed(2)}</span>
          </Text>
        );
      },
    },
    {
      title: '竞价调整',
      key: 'bid',
      width: 140,
      render: (_: any, record: API.CampaignAdjustmentItem) => {
        if (!record.bid_old || !record.bid_new) return <Text>-</Text>;
        const bid_old = Number(record.bid_old);
        const bid_new = Number(record.bid_new);
        return (
          <Text>
            {formatBidPercentage(bid_old)} →  <span style={{ color: getBudgetColor(bid_old, bid_new) }}>{formatBidPercentage(bid_new)}</span>
          </Text>
        );
      },
    },
    {
      title: '调整理由',
      dataIndex: 'rationale',
      key: 'rationale',
      width: 300,
      render: (rationale: string) => {
        return <Text>{rationale || '-'}</Text>;
      },
    },
  ];

  const budgetValueStyle: React.CSSProperties = {
    fontSize: '28px',
    fontWeight: 'bold',
    color: '#303133',
  };

  const placementColumns = [
    {
      title: '广告位类型',
      dataIndex: 'type',
      key: 'type',
      width: 200,
      fixed: 'left' as const,
      render: (text: string) => <Text>{text}</Text>,
    },
    {
      title: '花费',
      dataIndex: 'current_spend',
      key: 'current_spend',
      width: 100,
      render: (current_spend: number | string) => {
        if (current_spend === undefined || current_spend === null) return <Text>-</Text>;
        return <Text>${Number(current_spend).toFixed(2)}</Text>;
      },
    },
    {
      title: '销售额',
      dataIndex: 'current_sales',
      key: 'current_sales',
      width: 100,
      render: (current_sales: number | string) => {
        if (current_sales === undefined || current_sales === null) return <Text>-</Text>;
        return <Text>${Number(current_sales).toFixed(2)}</Text>;
      },
    },
    {
      title: 'ACoS',
      dataIndex: 'current_acos',
      key: 'current_acos',
      width: 100,
      render: (current_acos: string) => {
        if (current_acos === undefined || current_acos === null) return <Text>-</Text>;
        return <Text>{processNumberOrString(current_acos)}%</Text>;
      },
    },
    {
      title: 'CVR',
      dataIndex: 'current_cvr',
      key: 'current_cvr',
      width: 100,
      render: (current_cvr: string) => {
        if (current_cvr === undefined || current_cvr === null) return <Text>-</Text>;
        return <Text>{processNumberOrString(current_cvr)}%</Text>;
      },
    },
    {
      title: '竞价调整',
      key: 'bid_adjustment',
      width: 140,
      render: (_: any, record: API.PlacementAdjustmentItem) => {
        const { old_bid, new_bid } = record;
        if (!old_bid && !new_bid) return <Text>-</Text>;
        const old_bid_num = Number(old_bid);
        const new_bid_num = Number(new_bid);
        return (
          <Text>
            ${old_bid_num.toFixed(2)} <ArrowRightOutlined style={{ margin: '0 4px' }} /> ${new_bid_num.toFixed(2)}
          </Text>
        );
      },
    },
    {
      title: '调整比例',
      key: 'adjustment_ratio',
      width: 140,
      render: (_: any, record: API.PlacementAdjustmentItem) => {
        const { adjustment_ratio_old, adjustment_ratio_new } = record;
        return (
          <Text>
            {formatBidPercentage(adjustment_ratio_old)} <ArrowRightOutlined style={{ margin: '0 4px' }} /> {formatBidPercentage(adjustment_ratio_new)}
          </Text>
        );
      },
    },
    {
      title: '调整理由',
      key: 'rationale',
      width: 300,
      render: (_: any, record: API.PlacementAdjustmentItem) => {
        return <Text>{record.rationale || '-'}</Text>;
      },
    },
  ];

  return (
    <div className='report-modal-div'>
      <div className='report-modal-content' style={{ marginTop: '32px' }}>
        {/* 时间按钮组 - 当 daypartReports 数组长度大于1时显示 */}
        {daypartReports.length > 1 && (
          <div style={{ marginBottom: '24px' }}>
            <Row gutter={[8, 8]}>
              {daypartReports.map((item, index) => (
                <Col key={index}>
                  <Button
                    type={selectedTimeIndex === index ? 'primary' : 'default'}
                    onClick={() => handleTimeButtonClick(index)}
                  >
                    {formatTimeDisplay(item.current_time, country)}
                  </Button>
                </Col>
              ))}
            </Row>
          </div>
        )}

        {
          !proposal ?
            <Empty description="暂无数据" />
            :
            <>
              <DailyProgressAnalysis data={proposal?.day_progress_analysis} overallRationale={proposal.overall_rationale} />

              <Title level={3} style={{ marginTop: '1.5em', marginBottom: '1em' }}>投放策略调整</Title>
              {/* Section 2: 预算调整 */}
              {proposal.adjustments.daily_budget && (
                <Card className="card" style={{ marginBottom: '1.5em' }}>
                  <Title level={4}>预算调整</Title>
                  <Flex align="center" justify="flex-start" gap="middle" style={{ marginTop: 12, marginBottom: 12 }}>
                    <Text style={budgetValueStyle}>${Number(proposal.adjustments.daily_budget.old).toFixed(0)}</Text>
                    <Flex vertical={true} gap="2px">
                      <span>调整至</span>
                      <ArrowRightOutlined style={{ fontSize: '20px', margin: '0 16px', color: '#606266' }} />
                    </Flex>
                    <Text style={{ ...budgetValueStyle, color: '#D9001B' }}>${Number(proposal.adjustments.daily_budget.new).toFixed(0)}</Text>
                  </Flex>
                  <Text style={{ whiteSpace: 'pre-line', lineHeight: '1.8', color: '#303133', fontSize: '14px', }}>
                    {proposal.adjustments.daily_budget.rationale}
                  </Text>
                </Card>
              )}


              {/* Section 3: Campaign预算调整 */}
              {proposal.adjustments.campaign && proposal.adjustments.campaign.length > 0 && (
                <>
                  <Card className="card" style={{ marginBottom: '1.5em' }}>
                    <Title level={4}>Campaign预算调整</Title>
                    <Paragraph>*实际投放时，AI 会根据实际投放表现动态调整预算和竞价，除非需要大幅度修改，一般不建议修改 Campaign 的预算和竞价</Paragraph>
                    <Table
                      columns={campaignColumns}
                      dataSource={proposal.adjustments.campaign}
                      rowKey="campaign_id"
                      pagination={false}
                      size="middle"
                      scroll={{ x: 1280 }}
                    />
                  </Card>
                </>
              )}

              {/* Section 4: 广告位竞价策略 */}
              {proposal.adjustments.placement && proposal.adjustments.placement.length > 0 && (
                <>
                  <Card className="card" style={{ marginBottom: '1.5em' }}>
                    <Title level={4}>广告位竞价策略</Title>
                    <Table
                      columns={placementColumns}
                      dataSource={proposal.adjustments.placement}
                      rowKey={(record) => record.type}
                      pagination={false}
                      size="middle"
                      scroll={{ x: 1280 }}
                    />
                  </Card>
                </>
              )}
            </>
        }
      </div>
    </div>
  );
};

export default DayStrategyCatContent;
