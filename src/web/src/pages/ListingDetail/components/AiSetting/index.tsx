import React, { useState } from 'react';
import { Card, Divider, Switch } from 'antd';
import { getAvatarByType } from '../bus';
import styles from '../AiWork/style.less';
import { getListingSetting, updateListingSetting } from '@/services/ibidder_api/listings';
import { useSearchParams, useRequest } from '@umijs/max';


const AiSettingContainer: React.FC = () => {
  const [opened, setOpened] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;

  // 使用useRequest获取设置信息
  const { loading } = useRequest(
    () => getListingSetting({
      parent_asin: asin,
      profile_id: Number(profile_id),
    }),
    {
      ready: !!(asin && profile_id),
      refreshDeps: [asin, profile_id],
      onSuccess: (res) => {
        const ads_operation = res.setting?.ads_operation || false;
        setOpened(ads_operation);
      },
      onError: (error) => {
        console.error('获取设置信息失败:', error);
      }
    }
  );

  const handleSwitchChange = (checked: boolean) => {
    setOpened(checked);
    updateListingSetting({
      parent_asin: asin,
      profile_id: Number(profile_id),
      ads_operation: checked,
    });
  };

  return (
    <Card className="card" loading={loading}>
      <div style={{display: 'flex', alignItems: 'center', marginBottom: 16, marginTop: 16}}>
        {getAvatarByType('operAgent')}
        <div className={styles.aiWorkRole}>
          <span className={styles.aiWorkType}>广告优化师</span>
          <span className={styles.aiWorkDesc}>ROI操盘手</span>
        </div>
        <Divider type="vertical" style={{height: 48}}/>
        <div style={{marginLeft: 24, marginRight: 16, color: '#000000'}}>AI执行广告操作开关：</div>
        <Switch
          checkedChildren="开"
          unCheckedChildren="关"
          checked={opened}
          onChange={handleSwitchChange}
        />
        <div style={{color: '#979797', marginLeft: 16}}>
          <span style={{color: '#D80027'}}>*</span>
          开启后，AI将自动调整此产品的亚马逊广告
        </div>
      </div>

    </Card>
  );
};

export default AiSettingContainer;