@import '~antd/es/style/themes/default.less';

.dashboard-container {
  padding: 24px;

  .overview-section {
    margin-bottom: 24px;

    .overview-icon {
      font-size: 24px;
      color: @primary-color;
    }

    .ant-card {
      height: 100%;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      }
    }
  }

  .forecast-section {
    margin-bottom: 24px;

    .ant-card {
      height: 100%;
    }
  }

  .ant-card {
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.06);

    .ant-card-head {
      border-bottom: 1px solid @border-color-split;
      border-radius: 8px 8px 0 0;
      background: linear-gradient(to right, #fafafa, #f0f2f5);
      padding: 16px 24px;

      .ant-card-head-title {
        font-weight: 600;
        font-size: 16px;
      }
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  .ant-tabs {
    background: @component-background;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.06);

    .ant-tabs-nav {
      margin-bottom: 16px;

      &::before {
        border-bottom: 1px solid @border-color-split;
      }

      .ant-tabs-tab {
        padding: 12px 24px;
        font-size: 14px;
        transition: all 0.3s;

        &:hover {
          color: @primary-color;
        }

        &.ant-tabs-tab-active {
          font-weight: 600;
        }
      }
    }
  }

  .ant-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
      padding: 16px;
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
    }

    .ant-table-tbody > tr:hover > td {
      background: fade(@primary-color, 3%);
    }
  }

  .ant-descriptions {
    .ant-descriptions-item-label {
      font-weight: 500;
      color: @text-color-secondary;
    }

    .ant-descriptions-item-content {
      color: @text-color;
    }
  }

  .ant-tag {
    border-radius: 4px;
    padding: 2px 8px;
    margin-right: 8px;
    font-size: 12px;
  }

  .ant-typography {
    &.ant-typography-secondary {
      color: @text-color-secondary;
    }
  }

  .ant-statistic {
    .ant-statistic-title {
      color: @text-color-secondary;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .ant-statistic-content {
      font-size: 24px;
      font-weight: 600;
      color: @heading-color;
    }
  }
}