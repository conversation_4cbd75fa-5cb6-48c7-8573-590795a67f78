import React from 'react';
import { Card, Typography, List } from 'antd';
import { EyeOutlined } from '@ant-design/icons';

const { Title } = Typography;

interface MarketPreviewProps {
  previewData: string[];
}

const MarketPreview: React.FC<MarketPreviewProps> = ({ previewData }) => {
  return (
    <Card
      className="market-preview-card"
      style={{
        height: '100%',
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
        transition: 'all 0.3s ease',
      }}
      hoverable
    >
      <div style={{ padding: '8px 0' }}>
        <Title 
          level={4} 
          style={{ 
            marginBottom: '16px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <EyeOutlined style={{ color: '#1890ff' }} />
          市场预览
        </Title>
        <List
          dataSource={previewData}
          renderItem={(item) => (
            <List.Item
              style={{
                padding: '12px 16px',
                background: '#fafafa',
                borderRadius: '8px',
                marginBottom: '8px',
                fontSize: '14px',
                lineHeight: '1.5',
                color: 'rgba(0,0,0,0.85)',
                transition: 'all 0.3s ease',
                ':hover': {
                  background: '#f0f0f0',
                }
              }}
            >
              {item}
            </List.Item>
          )}
        />
      </div>
    </Card>
  );
};

export default MarketPreview;