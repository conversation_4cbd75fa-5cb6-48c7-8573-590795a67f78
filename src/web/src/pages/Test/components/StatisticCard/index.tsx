import React from 'react';
import { Card, Statistic, Space, Typography, Tooltip } from 'antd';
import { CaretUpOutlined, CaretDownOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';

const { Text } = Typography;

interface TrendData {
  value: number;
  type: 'up' | 'down';
  label: string;
}

interface FooterData {
  label: string;
  value: number;
  suffix?: string;
}

interface StatisticCardProps {
  title: string;
  mainValue: number;
  valueType: 'money' | 'percentage';
  icon: React.ReactNode;
  weeklyTrend: TrendData;
  dailyTrend: TrendData;
  footerData: FooterData;
}

export const StatisticCard: React.FC<StatisticCardProps> = ({
  title,
  mainValue,
  valueType,
  icon,
  weeklyTrend,
  dailyTrend,
  footerData,
}) => {
  const formatValue = (value: number, type: 'money' | 'percentage') => {
    if (type === 'money') {
      return `$${value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
    return `${value}%`;
  };

  const renderTrend = (trend: TrendData) => (
    <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
      {trend.type === 'up' ? (
        <CaretUpOutlined style={{ color: '#52c41a' }} />
      ) : (
        <CaretDownOutlined style={{ color: '#ff4d4f' }} />
      )}
      <span
        style={{
          color: trend.type === 'up' ? '#52c41a' : '#ff4d4f',
          fontSize: '14px',
        }}
      >
        {trend.value}%
      </span>
      <Text type="secondary" style={{ fontSize: '14px' }}>
        {trend.label}
      </Text>
    </div>
  );

  return (
    <ProCard
      className="statistic-card"
      style={{
        height: '100%',
        background: 'linear-gradient(to bottom right, #ffffff, #fafafa)',
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
        transition: 'all 0.3s ease',
      }}
      hoverable
    >
      <div style={{ padding: '20px' }}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          {/* 标题和图标 */}
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Text type="secondary" style={{ fontSize: '16px' }}>
              {title}
            </Text>
            <span
              style={{
                fontSize: '24px',
                color: '#1890ff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '40px',
                height: '40px',
                borderRadius: '20px',
                background: '#f0f5ff',
              }}
            >
              {icon}
            </span>
          </div>

          {/* 主要数值 */}
          <Statistic
            value={mainValue}
            valueStyle={{
              fontSize: '30px',
              fontWeight: 600,
              color: '#262626',
            }}
            formatter={(value) => formatValue(value as number, valueType)}
          />

          {/* 趋势数据 */}
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            {renderTrend(weeklyTrend)}
            {renderTrend(dailyTrend)}
          </Space>

          {/* 分割线 */}
          <div
            style={{
              height: '1px',
              background: '#f0f0f0',
              margin: '8px 0',
            }}
          />

          {/* 底部数据 */}
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Text type="secondary">{footerData.label}</Text>
            <Text strong>
              {footerData.value}
              {footerData.suffix && (
                <Text type="secondary" style={{ marginLeft: '4px' }}>
                  {footerData.suffix}
                </Text>
              )}
            </Text>
          </div>
        </Space>
      </div>
    </ProCard>
  );
};