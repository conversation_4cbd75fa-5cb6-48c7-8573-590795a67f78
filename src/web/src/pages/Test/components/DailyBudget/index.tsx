import React from 'react';
import { Table, Typography, Tag, Tooltip } from 'antd';
import { ProCard } from '@ant-design/pro-components';
import { InfoCircleOutlined } from '@ant-design/icons';
import type { WeekStrategyData } from '../../types';

const { Text } = Typography;

interface DailyBudgetProps {
  dailyStrategy: WeekStrategyData['target_week_strategy']['week_approach'];
}

const DailyBudget: React.FC<DailyBudgetProps> = ({ dailyStrategy }) => {
  const columns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      fixed: 'left' as const,
    },
    {
      title: '预算',
      dataIndex: 'budget',
      key: 'budget',
      width: 100,
      render: (budget: number) => `$${budget}`,
    },
    {
      title: (
        <span>
          预算使用率
          <Tooltip title="上周同期预算使用情况">
            <InfoCircleOutlined style={{ marginLeft: 4 }} />
          </Tooltip>
        </span>
      ),
      dataIndex: 'rationale',
      key: 'budget_usage',
      width: 120,
      render: (rationale: string) => {
        const match = rationale.match(/预算使用率\((\d+\.\d+)%\)/);
        return match ? (
          <Tag color={parseFloat(match[1]) > 80 ? 'green' : 'blue'}>
            {match[1]}%
          </Tag>
        ) : null;
      },
    },
    {
      title: '出价调整',
      dataIndex: 'bid_adjustment',
      key: 'bid_adjustment',
      width: 120,
      render: (adjustment: { min: string; max: string }) => (
        <Tag color="blue">{adjustment.min === adjustment.max ? adjustment.min : `${adjustment.min} ~ ${adjustment.max}`}</Tag>
      ),
    },
    {
      title: '历史表现',
      dataIndex: 'rationale',
      key: 'performance',
      width: 200,
      render: (rationale: string) => {
        const acos = rationale.match(/ACOS\((\d+\.\d+)%\)/);
        const cvr = rationale.match(/CVR达(\d+\.\d+)%/);
        return (
          <span>
            {acos && <Tag color="volcano">ACOS: {acos[1]}%</Tag>}
            {cvr && <Tag color="green" style={{ marginLeft: 4 }}>CVR: {cvr[1]}%</Tag>}
          </span>
        );
      },
    },
    {
      title: '调整理由',
      dataIndex: 'rationale',
      key: 'rationale',
      ellipsis: true,
    },
  ];

  return (
    <ProCard
      title="每日预算分配"
      headerBordered
      bordered
    >
      <Table
        columns={columns}
        dataSource={dailyStrategy}
        rowKey="date"
        pagination={false}
        size="small"
        scroll={{ x: 1000 }}
      />
    </ProCard>
  );
};

export default DailyBudget;