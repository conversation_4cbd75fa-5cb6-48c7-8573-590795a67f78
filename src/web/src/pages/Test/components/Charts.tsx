import React from 'react';
import { Area, Column } from '@ant-design/plots';
import { Progress } from 'antd';
import type { ChartDataType, ChartConfig } from '../types';

export const VisitChart: React.FC<{
  data: ChartDataType[];
  config?: ChartConfig;
}> = ({ data, config }) => {
  if (!data || data.length === 0) return null;

  const areaConfig = {
    data,
    xField: 'date',
    yField: 'value',
    smooth: true,
    areaStyle: {
      fill: 'l(270) 0:#ffffff 1:#1890ff',
    },
    line: {
      color: '#1890ff',
    },
    xAxis: false,
    yAxis: false,
    height: config?.height || 60,
    padding: config?.padding || [0, 0, 0, 0],
    autoFit: config?.autoFit ?? true,
  };

  return <Area {...areaConfig} />;
};

export const PaymentChart: React.FC<{
  data: ChartDataType[];
  config?: ChartConfig;
}> = ({ data, config }) => {
  if (!data || data.length === 0) return null;

  const columnConfig = {
    data,
    xField: 'date',
    yField: 'value',
    columnWidthRatio: 0.6,
    color: '#1890ff',
    grid: {
      line: {
        style: {
          stroke: '#f0f0f0',
          lineWidth: 1,
          lineDash: [4, 4],
        },
      },
    },
    height: config?.height || 100,
    padding: config?.padding || [20, 0, 20, 0],
    autoFit: config?.autoFit ?? true,
  };

  return <Column {...columnConfig} />;
};

export const ActivityProgress: React.FC<{
  percent: number;
}> = ({ percent }) => {
  const validPercent = Math.max(0, Math.min(100, percent));

  return (
    <Progress
      percent={validPercent}
      strokeColor={{
        '0%': '#108ee9',
        '100%': '#87d068',
      }}
      strokeWidth={6}
      status="active"
    />
  );
};