import React from 'react';
import { ProCard } from '@ant-design/pro-components';
import { List, Typography, Space } from 'antd';
import type { WeekStrategyData } from '../../types';

const { Text } = Typography;

interface AdsGoalAndSuggestionsProps {
  adsGoal: WeekStrategyData['target_week_strategy']['week_ads_goal'];
  suggestions: string[];
}

const AdsGoalAndSuggestions: React.FC<AdsGoalAndSuggestionsProps> = ({ adsGoal, suggestions }) => {
  return (
    <Space direction="vertical" style={{ width: '100%', marginBottom: '24px' }}>
      <ProCard title="广告目标" headerBordered bordered>
        <div style={{ marginBottom: 16 }}>
          <Text strong>主要目标：</Text>
          <div style={{ marginTop: 8 }}>
            <Text>{adsGoal.primary_goal.goal}</Text>
            <br />
            <Text type="secondary">{adsGoal.primary_goal.rationale}</Text>
          </div>
        </div>
        <div>
          <Text strong>其他目标：</Text>
          <List
            style={{ marginTop: 8 }}
            size="small"
            dataSource={adsGoal.other_goals}
            renderItem={(item) => (
              <List.Item>
                <Text>{item}</Text>
              </List.Item>
            )}
          />
        </div>
      </ProCard>
      
      <ProCard title="卖家建议" headerBordered bordered>
        <List
          size="small"
          dataSource={suggestions}
          renderItem={(item) => (
            <List.Item>
              <Text>{item}</Text>
            </List.Item>
          )}
        />
      </ProCard>
    </Space>
  );
};

export default AdsGoalAndSuggestions;