import React from 'react';
import { Card, Row, Col, Typography } from 'antd';
import { LikeOutlined, DislikeOutlined, ThunderboltOutlined, WarningOutlined } from '@ant-design/icons';
import { SwotData } from '../types';

const { Title } = Typography;

interface SWOTAnalysisProps {
  swotData: SwotData;
}

const SWOTAnalysis: React.FC<SWOTAnalysisProps> = ({ swotData }) => {
  const swotConfig = {
    strengths: {
      icon: <LikeOutlined />,
      color: '#52c41a',
      bgColor: '#f6ffed',
      borderColor: '#b7eb8f',
      title: '优势 (Strengths)',
    },
    weaknesses: {
      icon: <DislikeOutlined />,
      color: '#ff4d4f',
      bgColor: '#fff2f0',
      borderColor: '#ffccc7',
      title: '劣势 (Weaknesses)',
    },
    opportunities: {
      icon: <ThunderboltOutlined />,
      color: '#1890ff',
      bgColor: '#e6f7ff',
      borderColor: '#91d5ff',
      title: '机会 (Opportunities)',
    },
    threats: {
      icon: <WarningOutlined />,
      color: '#faad14',
      bgColor: '#fff7e6',
      borderColor: '#ffd591',
      title: '威胁 (Threats)',
    },
  };

  return (
    <div className="swot-analysis" style={{ marginBottom: '24px' }}>
      <Title level={4} className="section-title" style={{ marginBottom: '16px', color: '#1f1f1f' }}>
        市场分析 (SWOT)
      </Title>
      <Row gutter={[16, 16]}>
        {Object.entries(swotData).map(([key, items]) => (
          <Col xs={24} sm={12} md={6} key={key}>
            <Card
              className={`swot-card swot-${key}`}
              style={{
                backgroundColor: swotConfig[key as keyof typeof swotConfig].bgColor,
                border: `1px solid ${swotConfig[key as keyof typeof swotConfig].borderColor}`,
                borderRadius: '12px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                transition: 'all 0.3s ease',
                height: '100%',
                ':hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 6px 16px rgba(0,0,0,0.08)',
                },
              }}
              title={
                <span 
                  style={{ 
                    color: swotConfig[key as keyof typeof swotConfig].color,
                    fontSize: '16px',
                    fontWeight: 600,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                >
                  {swotConfig[key as keyof typeof swotConfig].icon}
                  {swotConfig[key as keyof typeof swotConfig].title}
                </span>
              }
              bodyStyle={{ padding: '16px' }}
            >
              <ul 
                className="swot-list" 
                style={{ 
                  listStyle: 'none',
                  padding: 0,
                  margin: 0,
                }}
              >
                {items.map((item: string, index: number) => (
                  <li 
                    key={index} 
                    style={{
                      marginBottom: '12px',
                      padding: '8px 12px',
                      backgroundColor: 'rgba(255,255,255,0.6)',
                      borderRadius: '6px',
                      fontSize: '14px',
                      lineHeight: '1.5',
                      color: 'rgba(0,0,0,0.85)',
                      boxShadow: '0 1px 2px rgba(0,0,0,0.03)',
                    }}
                  >
                    {item}
                  </li>
                ))}
              </ul>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default SWOTAnalysis;