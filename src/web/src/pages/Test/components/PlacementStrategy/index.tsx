import React from 'react';
import { Table, Tag } from 'antd';
import { ProCard } from '@ant-design/pro-components';
import type { WeekStrategyData } from '../../types';

interface PlacementStrategyProps {
  placementData: WeekStrategyData['target_week_strategy']['placement_bidding'];
}

const PlacementStrategy: React.FC<PlacementStrategyProps> = ({ placementData }) => {
  const columns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 120,
    },
    {
      title: '搜索页顶部',
      dataIndex: ['placement', 'top_of_search'],
      key: 'top_of_search',
      width: 120,
      render: (value: string) => <Tag color="blue">{value}</Tag>,
    },
    {
      title: '商品详情页',
      dataIndex: ['placement', 'product_detail_page'],
      key: 'product_detail_page',
      width: 120,
      render: (value: string) => <Tag color="green">{value}</Tag>,
    },
    {
      title: '其他位置',
      dataIndex: ['placement', 'other'],
      key: 'other',
      width: 120,
      render: (value: string) => <Tag color="orange">{value}</Tag>,
    },
    {
      title: '调整理由',
      dataIndex: 'rationale',
      key: 'rationale',
      ellipsis: true,
    },
  ];

  const dataSource = placementData.map((item) => {
    const date = Object.keys(item)[0];
    return {
      date,
      ...item[date],
    };
  });

  return (
    <ProCard
      title="广告位置策略"
      headerBordered
      bordered
    >
      <Table
        columns={columns}
        dataSource={dataSource}
        rowKey="date"
        pagination={false}
        size="small"
      />
    </ProCard>
  );
};

export default PlacementStrategy;