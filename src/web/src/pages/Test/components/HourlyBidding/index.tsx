import React from 'react';
import { Column } from '@ant-design/plots';
import { ProCard } from '@ant-design/pro-components';
import type { WeekStrategyData } from '../../types';

interface HourlyBiddingProps {
  hodData: WeekStrategyData['target_week_strategy']['hod_bidding'];
  rational: string;
}

const HourlyBidding: React.FC<HourlyBiddingProps> = ({ hodData, rational }) => {
  const data = Object.entries(hodData).map(([hour, adjustment]) => ({
    hour: `${hour}:00`,
    adjustment: parseFloat(adjustment.replace('%', '')),
  }));

  const config = {
    data,
    xField: 'hour',
    yField: 'adjustment',
    label: {
      position: 'middle',
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
      formatter: (datum: { adjustment: number }) => `${datum.adjustment}%`,
    },
    xAxis: {
      label: {
        autoRotate: true,
      },
    },
    color: (datum: { adjustment: number }) => {
      if (datum.adjustment > 0) return '#52c41a';
      if (datum.adjustment < 0) return '#ff4d4f';
      return '#1890ff';
    },
  };

  return (
    <ProCard
      title="小时级别出价策略"
      headerBordered
      bordered
    >
      <Column {...config} height={300} />
      <div style={{ marginTop: 16, padding: '0 24px' }}>
        <p style={{ color: 'rgba(0,0,0,0.65)' }}>{rational}</p>
      </div>
    </ProCard>
  );
};

export default HourlyBidding;