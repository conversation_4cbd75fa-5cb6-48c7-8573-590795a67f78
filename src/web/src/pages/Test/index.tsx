import React from 'react';
import { Card, Statistic, Row, Col, Typography } from 'antd';
import { ArrowRightOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

const data = {
  overallStrategy: {
    title: '总体策略',
    strategy: '平衡',
    description:
      '当前处于季节性需求转换期，市场正从低谷缓慢恢复，但尚未达到高峰。采取平衡策略既能把握市场回暖机会，又能控制成本风险，为未来更强劲的需求做好准备',
  },
  weeklyForecast: {
    title: '周预算',
    budget: '$ 1350',
    range: '$1000 ~ $1500',
    description:
      '类目流量显示春季需求开始回升，同时88.99%的高预算使用率表明广告需求强劲，小幅提高预算可抓住市场回暖机会，同时控制风险',
  },
  bidAdjustment: {
    title: '竞价调整',
    adjustment: '-5% - 10%',
    description:
      '保持竞价相对稳定以维持当前位置，同时针对高转化时段适度提高竞价，低效时段略微降低竞价，优化整体效率',
  },
  expectedResults: {
    advertisingExpenditure: {
      title: '广告支出',
      value: '$ 1200',
      range: '$1000 ~ $1500',
    },
    advertisingSales: {
      title: '广告销售额',
      value: '$ 3100',
      range: '$2800 ~ $3400',
    },
    acos: {
      title: 'ACoS',
      value: '34.8%',
      range: '32%~ 38%',
    },
    conversionRate: {
      title: '转化率',
      value: '13.5%',
      range: '12.5% ~ 14.5%',
    },
  },
  marketAssessment: {
    strengths: [
      '1. 近期广告表现稳定，上周ACoS为34.91%，低于14周平均值38.58%',
      '2. 上周转化率达到14.08%，高于14周平均值12.98%',
      '3. 上周销售额达到2645.02美元，高于14周平均值1900.85美元',
      '4. 产品价格稳定在26.99美元，有利于维持利润率',
    ],
    weaknesses: [
      '1. 预算使用率较高(85.58%)，可能限制了潜在销售增长',
      '2. CPC略高于平均水平(1.33美元 vs 1.32美元)',
      '3. 点击率(0.47%)略低于14周平均值(0.48%)',
    ],
    opportunities: [
      '1. 2月底至3月初是宠物护理季节性需求上升期',
      '2. 根据类目流量数据，3月初通常比2月底有更高的流量',
      '3. 周三和周四历史表现较好，可针对性提高这些日期的预算',
      '4. 月初(1-3日)通常有较高的转化潜力',
    ],
    threats: [
      '1. 类目整体流量呈下降趋势，2月流量低于去年同期',
      '2. 竞争对手可能在季节转换期增加广告投入',
      '3. CPC持续上涨可能影响广告效率',
      '4. 月底预算消耗过快可能导致错过高转化机会',
    ],
  },
};

const InvestmentStrategy: React.FC = () => {

  return (
    <div style={{ padding: '24px' }}>
      <Title level={4} style={{ marginBottom: 24, fontWeight: 'bold', fontSize: '18px' }}>
        投放策略
      </Title>

      <Row gutter={16}>
        <Col span={8}>
          <Card style={{borderRadius: 8, boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)', minHeight: 200 }}>
            <Title level={5} style={{ fontSize: '14px', fontWeight: 'normal', marginBottom: 8 }}>{data.overallStrategy.title}</Title>
            <div style={{ fontSize: '2em', fontWeight: 'bold', color: '#1890ff', marginBottom: '8px' }}>{data.overallStrategy.strategy}</div>
            <Text style={{fontSize: '12px', color: '#666'}}>{data.overallStrategy.description}</Text>
          </Card>
        </Col>
        <Col span={8}>
          <Card style={{borderRadius: 8, boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)', minHeight: 200 }}>
            <Title level={5} style={{ fontSize: '14px', fontWeight: 'normal', marginBottom: 8 }}>{data.weeklyForecast.title}</Title>
            <div style={{ fontSize: '2em', fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
              {data.weeklyForecast.budget}
              <ArrowRightOutlined style={{ fontSize: '12px', marginLeft: '8px', color: '#1890ff' }} />
            </div>
            <Text style={{fontSize: '12px', color: '#666'}}>{data.weeklyForecast.range}</Text>
            <br />
            <Text style={{fontSize: '12px', color: '#666'}}>{data.weeklyForecast.description}</Text>
          </Card>
        </Col>
        <Col span={8}>
          <Card style={{borderRadius: 8, boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)', minHeight: 200 }}>
            <Title level={5} style={{ fontSize: '14px', fontWeight: 'normal', marginBottom: 8 }}>{data.bidAdjustment.title}</Title>
            <div style={{ fontSize: '2em', fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
              {data.bidAdjustment.adjustment}
              <ArrowRightOutlined style={{ fontSize: '12px', marginLeft: '8px', color: '#1890ff' }} />
            </div>
            <Text style={{fontSize: '12px', color: '#666'}}>{data.bidAdjustment.description}</Text>
          </Card>
        </Col>
      </Row>

      <Title level={4} style={{ marginTop: 24, marginBottom: 24, fontWeight: 'bold', fontSize: '18px', textAlign: 'left' }}>
        预期结果
      </Title>

      <Row gutter={16}>
        <Col span={6}>
          <Card style={{borderRadius: 8, boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)', minHeight: 130}}>
            <Statistic
              title={<div style={{fontSize: '14px', color: '#8c8c8c'}}>{data.expectedResults.advertisingExpenditure.title}</div>}
              valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
              value={data.expectedResults.advertisingExpenditure.value}
            />
            <Text style={{fontSize: '12px', color: '#666'}}>{data.expectedResults.advertisingExpenditure.range}</Text>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{borderRadius: 8, boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)', minHeight: 130}}>
            <Statistic
              title={<div style={{fontSize: '14px', color: '#8c8c8c'}}>{data.expectedResults.advertisingSales.title}</div>}
              valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
              value={data.expectedResults.advertisingSales.value}
            />
            <Text style={{fontSize: '12px', color: '#666'}}>{data.expectedResults.advertisingSales.range}</Text>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{borderRadius: 8, boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)', minHeight: 130}}>
            <Statistic
              title={<div style={{fontSize: '14px', color: '#8c8c8c'}}>{data.expectedResults.acos.title}</div>}
              valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
              value={data.expectedResults.acos.value}
            />
            <Text style={{fontSize: '12px', color: '#666'}}>{data.expectedResults.acos.range}</Text>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{borderRadius: 8, boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)', minHeight: 130}}>
            <Statistic
              title={<div style={{fontSize: '14px', color: '#8c8c8c'}}>{data.expectedResults.conversionRate.title}</div>}
              valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
              value={data.expectedResults.conversionRate.value}
            />
            <Text style={{fontSize: '12px', color: '#666'}}>{data.expectedResults.conversionRate.range}</Text>
          </Card>
        </Col>
      </Row>

      <Title level={4} style={{ marginTop: 24, marginBottom: 24, fontWeight: 'bold', fontSize: '18px' }}>
        市场评估
      </Title>

      <Card style={{borderRadius: 8, boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'}}>
        <Row gutter={16}>
          <Col span={12}>
            <Card style={{ backgroundColor: '#e6f7ff', borderRadius: 8, border: 'none', minHeight: 200, marginBottom: 16, borderLeft: '4px solid #1890ff', position: 'relative' }}>
              <Title level={5} style={{ color: '#1890ff', fontSize: '18px', fontWeight: 'bold', textAlign: 'center' }}>优势 (Strengths)</Title>
              <ul style={{padding: '0', margin: '0'}}>
                {data.marketAssessment.strengths.map((item, index) => (
                  <li key={index} style={{fontSize: '16px', margin: '4px 0'}}>
                    <Text style={{fontSize: '16px', textAlign: 'center'}}>{item}</Text>
                  </li>
                ))}
              </ul>
            </Card>

            <Card style={{ marginTop: 16, backgroundColor: '#f6ffed', borderRadius: 8, border: 'none', minHeight: 200, marginBottom: 16, borderLeft: '4px solid #52c41a', position: 'relative' }}>
              <Title level={5} style={{ color: '#52c41a', fontSize: '18px', fontWeight: 'bold', textAlign: 'center' }}>机会 (Opportunities)</Title>
              <ul style={{padding: '0', margin: '0'}}>
                {data.marketAssessment.opportunities.map((item, index) => (
                  <li key={index} style={{fontSize: '16px', margin: '4px 0'}}>
                    <Text style={{fontSize: '16px', textAlign: 'center'}}>{item}</Text>
                  </li>
                ))}
              </ul>
            </Card>
          </Col>
          <Col span={12}>
            <Card style={{ backgroundColor: '#ffebe6', borderRadius: 8, border: 'none', minHeight: 200, borderLeft: '4px solid #f5222d', position: 'relative' }}>
              <Title level={5} style={{ color: '#f5222d', fontSize: '18px', fontWeight: 'bold', textAlign: 'center' }}>劣势 (Weaknesses)</Title>
              <ul style={{padding: '0', margin: '0'}}>
                {data.marketAssessment.weaknesses.map((item, index) => (
                  <li key={index} style={{fontSize: '16px', margin: '4px 0'}}>
                    <Text style={{fontSize: '16px', textAlign: 'center'}}>{item}</Text>
                  </li>
                ))}
              </ul>
            </Card>

            <Card style={{ marginTop: 16, backgroundColor: '#fef9e7', borderRadius: 8, border: 'none', minHeight: 200, marginBottom: 16, borderLeft: '4px solid #d4b106', position: 'relative' }}>
              <Title level={5} style={{ color: '#d4b106', fontSize: '18px', fontWeight: 'bold', textAlign: 'center' }}>威胁 (Threats)</Title>
              <ul style={{padding: '0', margin: '0'}}>
                {data.marketAssessment.threats.map((item, index) => (
                  <li key={index} style={{fontSize: '16px', margin: '4px 0'}}>
                    <Text style={{fontSize: '16px', textAlign: 'center'}}>{item}</Text>
                  </li>
                ))}
              </ul>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default InvestmentStrategy;
