//国家和时区对应关系
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { FormInstance } from 'antd';
import { getMessages } from '@/services/ibidder_api/user';
import { approveReport } from '@/services/ibidder_api/operation';

// 扩展dayjs以支持时区
dayjs.extend(utc);
dayjs.extend(timezone);

export const countryTimezoneMap = {
  'us': 'America/Los_Angeles',
  'uk': 'Europe/London',
  'de': 'Europe/Berlin',
  'fr': 'Europe/Paris',
  'it': 'Europe/Rome',
  'es': 'Europe/Madrid',
  'nl': 'Europe/Amsterdam',
  'be': 'Europe/Brussels',
  'se': 'Europe/Stockholm',
  'cn': 'Asia/Shanghai',
  'jp': 'Asia/Tokyo',
  'kr': 'Asia/Seoul',
  'au': 'Australia/Sydney',
  'ca': 'America/Toronto',
  'mx': 'America/Mexico_City',
  'br': 'America/Sao_Paulo',
  'ar': 'America/Buenos_Aires',
  'in': 'Asia/Kolkata',
  'sa': 'Asia/Riyadh',
  'ae': 'Asia/Dubai',
  'il': 'Asia/Jerusalem',
  'tr': 'Europe/Istanbul'
}

// 根据当前时间获取对应国家的时间
export const getCountryTimezone = (country: string, format?: string) => {
  // 获取时区
  const countryTimezone = countryTimezoneMap[country.toLowerCase() as keyof typeof countryTimezoneMap];

  // 使用当前时间并转换到指定国家的时区
  const timeObj = dayjs().tz(countryTimezone);

  // 如果提供了格式，按照指定格式返回；否则返回默认格式
  const formattedTime = format ? timeObj.format(format) : timeObj.format();

  return formattedTime;
}

// 获取星期几的函数
export const getDayOfWeek = (dateString?: string) => {
  if (!dateString) return '';
  const days = ['日', '一', '二', '三', '四', '五', '六'];
  return `星期${days[dayjs(dateString).day()]}`;
};

// 检查是否应该显示 ConfirmationSection 组件的时间控制逻辑
export const shouldShowConfirmationSection = (country: string, strategyType: 'day' | 'week' | 'month', date?: string): boolean => {
  if (!country) return false;

  // 获取站点时区的当前时间
  const countryTimezone = countryTimezoneMap[country.toLowerCase() as keyof typeof countryTimezoneMap];
  if (!countryTimezone) return false;

  const currentTime = dayjs().tz(countryTimezone);
  switch (strategyType) {
    case 'day':
      // 日策略：每日12点前显示
      if (date) {
        // 如果提供了日期，比较站点时间与指定日期的12:00
        const targetDateTime = dayjs(date).tz(countryTimezone).hour(12).minute(0).second(0);
        return currentTime.isBefore(targetDateTime);
      }
      return currentTime.hour() < 12;

    case 'week':
      // 周策略：每周五前显示（周一=1, 周五=5）
      return currentTime.day() < 5; // 0=周日, 1=周一, ..., 5=周五, 6=周六

    case 'month':
      // 月策略：每月20日前显示
      return currentTime.date() < 20;

    default:
      return false;
  }
};

// 默认颜色
export const colorPrimary = '#ed1000'

// 促销项类型
interface PromotionItem {
  time_range?: [string, string];
  promotion_type?: string;
  discount_price?: number | string;
}

// 这些类型仅用于 ProductInfoPartial 接口的定义

// 产品信息类型（部分字段）
interface ProductInfoPartial {
  promotion_plan?: {
    has_promotion?: boolean;
    promotion_list?: PromotionItem[];
  };
  inventory_status?: {
    sufficient_inventory?: boolean;
    quantity?: number;
  };
  competitor?: string[];
  [key: string]: any;
}

/**
 * 检查表单值是否与产品信息不一致
 * @param form 表单实例
 * @param productInfo 产品信息
 * @returns 如果表单值与产品信息不一致，返回 true；否则返回 false
 */
export const hasFormChanges = (form: FormInstance, productInfo: ProductInfoPartial): boolean => {
  try {
    if (!productInfo) return false;

    const formValues = form.getFieldsValue();

    // 检查促销计划是否有变化
    const formHasPromotion = formValues.promotion_plan?.has_promotion;
    const productHasPromotion = productInfo.promotion_plan?.has_promotion;
    if (formHasPromotion !== productHasPromotion) {
      return true;
    }

    // 如果都有促销，检查促销列表是否有变化
    if (formHasPromotion && productHasPromotion) {
      const formPromotionList = formValues.promotion_plan?.promotion_list || [];
      const productPromotionList = productInfo.promotion_plan?.promotion_list || [];

      // 如果长度不同，肯定有变化
      if (formPromotionList.length !== productPromotionList.length) {
        return true;
      }

      // 比较每个促销项
      for (let i = 0; i < formPromotionList.length; i++) {
        const formItem = formPromotionList[i];
        const productItem = productPromotionList[i];

        // 比较促销类型
        if (formItem.promotion_type !== productItem.promotion_type) {
          return true;
        }

        // 比较折扣价格
        if (Number(formItem.discount_price) !== Number(productItem.discount_price)) {
          return true;
        }

        // 比较时间范围
        if (JSON.stringify(formItem.time_range) !== JSON.stringify(productItem.time_range)) {
          return true;
        }
      }
    }

    // 检查库存情况是否有变化
    const formSufficientInventory = formValues.inventory_status?.sufficient_inventory;
    const productSufficientInventory = productInfo.inventory_status?.sufficient_inventory;
    if (formSufficientInventory !== productSufficientInventory) {
      return true;
    }

    // 如果都是库存不足，检查数量是否有变化
    if (formSufficientInventory === false && productSufficientInventory === false) {
      const formQuantity = Number(formValues.inventory_status?.quantity || 0);
      const productQuantity = Number(productInfo.inventory_status?.quantity || 0);
      if (formQuantity !== productQuantity) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('检查表单变化时出错:', error);
    return false;
  }
};



// 可复用的方法：根据 es_id 获取消息 id 并调用 approveReport
export const approveReportWithMessageId = async (params: {
  parent_asin: string;
  profile_id: string;
  country: string;
  es_id: string;
  job_id: string;
  asins: string[];
}) => {
  try {
    // 先调用 getMessages 获取消息列表
    const messagesResponse = await getMessages({
      is_read: 0,
      message_type: 5,
      page_no: 1,
      page_size: 20,
      parent_asin: params.parent_asin,
      profile_id: params.profile_id,
    });
    if (messagesResponse.data) {
      const messagesList = messagesResponse.data.list || [];

      // 根据 es_id 找到对应的消息
      const targetMessage = messagesList.find((msg: any) =>
        msg.extra_data?.es_id === params.es_id
      );
      const message_id = targetMessage?.id;

      if (targetMessage) {
        // 找到消息后，将消息 id 作为参数传入 approveReport
        await approveReport({
          ...params,
          message_id: message_id,
        } as any);
      } else {
        // 如果没找到对应的消息，仍然调用原来的 approveReport
        await approveReport(params);
      }
    } else {
      // 如果获取消息失败，仍然调用原来的 approveReport
      await approveReport(params);
    }
  } catch (error) {
    console.error('获取消息或调用 approveReport 失败:', error);
    // 出错时仍然调用原来的 approveReport
    await approveReport(params);
  }
};

/**
 * 生成版本选项的通用工具函数
 * @param maxVersionNumber 最大版本号
 * @returns 版本选项数组，格式为 [{label: '版本 1', value: 1}, ...]
 */
export const generateVersionOptions = (maxVersionNumber: number) => {
  const options = [];
  for (let i = 1; i <= maxVersionNumber; i++) {
    options.push({
      label: `版本 ${i}`,
      value: i
    });
  }
  return options;
};

/**
 * 处理数字或百分比字符串参数的方法
 * @param value 可能是数字或字符串或百分比字符串的参数
 * @param suffix 追加的后缀，默认没有
 * @param decimalPlaces 保留小数点位数，默认保留2位
 * @returns 根据不同情况返回处理后的值
 */
export const processNumberOrString = (
  value: number | string | undefined | null,
  suffix: string = '',
  decimalPlaces: number = 2
): string | number => {
  if (value === undefined || value === null) {
    return '-'; // 处理 undefined 或 null
  }

  let result: string | number;

  // 如果是字符串类型
  if (typeof value === 'string') {
    // 如果包含%，去除%返回字符串
    if (value.includes('%')) {
      result = value.replace('%', '');
    } else {
      // 如果是数字型字符串，转为数字乘100返回
      const numValue = Number(value);
      if (!isNaN(numValue)) {
        result = (numValue * 100).toFixed(decimalPlaces);
      } else {
        // 如果不是数字型字符串，直接返回原字符串
        result = value;
      }
    }
  } else if (typeof value === 'number') {
    // 如果是数字类型，乘100返回
    result = (value * 100).toFixed(decimalPlaces);
  } else {
    // 其他情况返回 '-'
    result = '-';
  }

  // 统一处理后缀
  return suffix ? result + suffix : result;
};


// 根据预算变化确定颜色
export const getBudgetColor = (oldValue: number, newValue: number): string => {
  if (newValue > oldValue) {
    return '#ff4d4f'; // 红色：预算增加
  } else if (newValue < oldValue) {
    return '#52c41a'; // 绿色：预算减少
  }
  return 'inherit'; // 默认颜色：预算不变
};