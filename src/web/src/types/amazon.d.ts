declare namespace API {
  // 商品基础信息
  interface ProductInfo {
    asin: string;
    title: string;
    price: number;
    currency: string;
    rating: number;
    reviewCount: number;
    mainImage: string;
    images: string[];
    description: string;
    features: string[];
    brand: string;
    category: string[];
    rank: string;
    dimensions?: {
      length: number;
      width: number;
      height: number;
      unit: string;
    };
    weight?: {
      value: number;
      unit: string;
    };
  }

  // API 响应格式
  interface ProductResponse {
    success: boolean;
    data?: ProductInfo;
    error?: string;
  }
} 