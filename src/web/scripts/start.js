const { spawn } = require('child_process');
const args = process.argv.slice(2);

// 检查是否包含 noauth 参数
const noAuth = args.includes('--noauth');

// 设置环境变量
const env = {
  ...process.env,
  REACT_APP_NO_AUTH: noAuth ? true : false,
  REACT_APP_ENV: 'dev',
  MOCK: 'none',
  UMI_ENV: 'dev',
  PORT: process.env.PORT || '8000',
  BROWSER: 'none',
};

// 使用 max dev 命令
const startProcess = spawn('max', ['dev'], {
  stdio: 'inherit',
  env,
  shell: true,
});

// 添加子进程事件监听
startProcess.on('spawn', () => {
  console.log('=== Start.js - Child process spawned ===');
});

startProcess.on('error', (err) => {
  console.error('Failed to start:', err);
  process.exit(1);
});