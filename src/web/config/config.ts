// https://umijs.org/config/
import { defineConfig } from '@umijs/max';
import defaultSettings from './defaultSettings';
import proxy from './proxy';
import routes from './routes';

const { REACT_APP_ENV = 'dev' } = process.env;

export default defineConfig({
  /**
   * @name 开启 hash 模式
   * @description 让 build 之后的产物包含 hash 后缀。通常用于增量发布和避免浏览器加载缓存。
   * @doc https://umijs.org/docs/api/config#hash
   */
  hash: true,

  /**
   * @name 路由的配置，不在路由中引入的文件不会编译
   * @description 只支持 path，component，routes，redirect，wrappers，title 的配置
   * @doc https://umijs.org/docs/guides/routes
   */
  routes,

  /**
   * @name 主题的配置
   * @description 虽然叫主题，但是其实只是 less 的变量设置
   * @doc antd的主题设置 https://ant.design/docs/react/customize-theme-cn
   * @doc umi 的theme 配置 https://umijs.org/docs/api/config#theme
   */
  theme: {
    'root-entry-name': 'variable',
  },

  /**
   * @name moment 的国际化配置
   * @description 如果对国际化没有要求，打开之后能减少js的包大小
   * @doc https://umijs.org/docs/api/config#ignoremomentlocale
   */
  ignoreMomentLocale: true,

  /**
   * @name 代理配置
   * @description 可以让你的本地服务器代理到你的服务器上，这样你就可以访问服务器的数据了
   * @doc 代理介绍 https://umijs.org/docs/guides/proxy
   * @doc 代理配置 https://umijs.org/docs/api/config#proxy
   */
  proxy: proxy[REACT_APP_ENV as keyof typeof proxy],

  /**
   * @name 快速热更新配置
   * @description 一个不错的热更新组件，更新时可以保留 state
   */
  fastRefresh: true,

  // ============== 插件配置 ===============
  antd: {
    theme: {
      token: defaultSettings.token,
    },
  },
  access: {},
  model: {},
  initialState: {},

  /**
   * @name layout 插件
   * @doc https://umijs.org/docs/max/layout-menu
   */
  title: 'FlyPower',
  layout: {
    locale: true,
    ...defaultSettings,
  },

  /**
   * @name moment2dayjs 插件
   * @description 将项目中的 moment 替换为 dayjs
   * @doc https://umijs.org/docs/max/moment2dayjs
   */
  moment2dayjs: {
    preset: 'antd',
    plugins: ['duration', 'isBetween', 'utc', 'timezone'],
  },

  /**
   * @name 国际化插件
   * @doc https://umijs.org/docs/max/i18n
   */
  locale: {
    default: 'zh-CN',
    antd: true,
    baseNavigator: true,
  },

  /**
   * @name 网络请求配置
   * @description 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
   * @doc https://umijs.org/docs/max/request
   */
  request: {},

  /**
   * @name <head> 中额外的 script
   * @description 配置 <head> 中额外的 script
   */
  headScripts: [
    { src: '/scripts/loading.js', async: true },
  ],

  //================ pro 插件配置 =================
  presets: ['umi-presets-pro'],

  /**
   * @name openAPI 插件的配置
   * @description 基于 openapi 的规范生成serve 和mock，能减少很多样板代码
   * @doc https://pro.ant.design/zh-cn/docs/openapi/
   */
  openAPI: [
    {
      requestLibPath: "import { request } from '@/request'",
      // schemaPath: "http://127.0.0.1:3000/api/v1/openapi.json",
      schemaPath: "http://*************:3000/api/v1/openapi.json",
      mock: false,
      projectName: "ibidder_api",
      namespace: 'API',
      hook: {
        customFunctionName: ((operation: {
          operationId?: string,
          path?: string,
          method?: string,
          summary?: string
        }) => {
          console.log('OpenAPI Generator params:', { operation });

          // 使用operation中的path和method
          const path = operation.path;
          const method = operation.method;

          if (!path) {
            return operation.operationId || 'UnknownOperation';
          }

          // 首字母大写的辅助函数
          const capitalize = (str: string) => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();

          // 使用summary作为函数名（如果存在）
          if (operation.summary) {
            // 将summary分割成单词，每个单词首字母大写
            return operation.summary
              .split(/\s+/)
              .map(capitalize)
              .join('');
          }

          // 移除版本号和多余的路径信息
          const pathParts = path.split('/').filter(p => p && !p.includes('api') && !p.includes('v1'));
          const operationId = pathParts.map(capitalize).join('');

          // 确保method也是首字母大写
          return method ? `${capitalize(method)}${operationId}` : operationId;
        }) as unknown as (() => any)
      }
    }
  ],

  mfsu: {
    strategy: 'normal',
  },

  esbuildMinifyIIFE: true,
  requestRecord: {},

  define: {
    'process.env.REACT_APP_NO_AUTH': process.env.REACT_APP_NO_AUTH === 'true',
  },

  mock: {
    include: ['mock/**/*.ts'],
  },

  npmClient: 'npm',
});
