{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "importHelpers": true, "jsx": "preserve", "esModuleInterop": true, "sourceMap": true, "baseUrl": "./", "skipLibCheck": true, "experimentalDecorators": true, "strict": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "types": ["node", "react", "react-dom", "aria-query"], "paths": {"@/*": ["./src/*"], "@@/*": ["./src/.umi/*"], "@@test/*": ["./src/.umi-test/*"]}}, "include": ["./**/*.d.ts", "./**/*.ts", "./**/*.tsx"]}